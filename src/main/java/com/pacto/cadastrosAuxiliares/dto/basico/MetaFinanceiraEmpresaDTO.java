package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@JsonInclude
@Schema(name = "Meta Financeira Empresa", description = "Informações de uma meta financeira de academia")
public class MetaFinanceiraEmpresaDTO {
    @Schema(description = "Código único identificador da meta financeira", example = "1")
    private Integer codigo;
    @Schema(description = "Mês da meta financeira", example = "3")
    private Integer mes;
    @Schema(description = "Ano da meta financeira", example = "2024")
    private Integer ano;
    @Schema(description = "Descrição da meta financeira da academia", example = "Meta de faturamento para março - Academia Pacto GO")
    private String descricao;
    @Schema(description = "Velocidade de receita da academia", example = "15000.50")
    private Double receitaVeloc;
    @Schema(description = "Velocidade de faturamento da academia", example = "18500.75")
    private Double faturamentoVeloc;
    @Schema(description = "Velocidade de despesa da academia", example = "8200.30")
    private Double despesaVeloc;
    @Schema(description = "Academia vinculada à meta financeira")
    private EmpresaDTO empresa;
    @Schema(description = "Lista de valores das metas financeiras da academia")
    private List<MetaFinanceiraEmpresaValoresDTO> valores = new ArrayList<>();
    @Schema(description = "Lista de personal trainers e instrutores vinculados à meta")
    private List<MetaFinanceiraConsultorDTO> consultores = new ArrayList<>();

    @Schema(description = "Período da meta no formato ano/mês", example = "2024/03")
    private String anoMes;
    @Schema(description = "Primeira meta de faturamento da academia", example = "R$ 10.000,00")
    private String meta1;
    @Schema(description = "Segunda meta de faturamento da academia", example = "R$ 15.000,00")
    private String meta2;
    @Schema(description = "Terceira meta de faturamento da academia", example = "R$ 20.000,00")
    private String meta3;
    @Schema(description = "Quarta meta de faturamento da academia", example = "R$ 25.000,00")
    private String meta4;
    @Schema(description = "Quinta meta de faturamento da academia", example = "R$ 30.000,00")
    private String meta5;

    @Schema(description = "Valor da meta atingida pela academia", example = "22500.80")
    private Double metaAtingida;
    @Schema(description = "Observações sobre a meta financeira da academia", example = "Meta superada devido ao aumento de matrículas no período")
    private String observacao;
    @Schema(description = "Código numérico do mês", example = "3")
    private Integer codigoMes;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getReceitaVeloc() {
        return receitaVeloc;
    }

    public void setReceitaVeloc(Double receitaVeloc) {
        this.receitaVeloc = receitaVeloc;
    }

    public Double getFaturamentoVeloc() {
        return faturamentoVeloc;
    }

    public void setFaturamentoVeloc(Double faturamentoVeloc) {
        this.faturamentoVeloc = faturamentoVeloc;
    }

    public Double getDespesaVeloc() {
        return despesaVeloc;
    }

    public void setDespesaVeloc(Double despesaVeloc) {
        this.despesaVeloc = despesaVeloc;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public List<MetaFinanceiraEmpresaValoresDTO> getValores() {
        return valores;
    }

    public void setValores(List<MetaFinanceiraEmpresaValoresDTO> valores) {
        this.valores = valores;
    }

    public Double getMetaAtingida() {
        return metaAtingida;
    }

    public void setMetaAtingida(Double metaAtingida) {
        this.metaAtingida = metaAtingida;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getCodigoMes() {
        return codigoMes;
    }

    public void setCodigoMes(Integer codigoMes) {
        this.codigoMes = codigoMes;
    }

    public String getMeta1() {
        return meta1;
    }

    public void setMeta1(String meta1) {
        this.meta1 = meta1;
    }

    public String getMeta2() {
        return meta2;
    }

    public void setMeta2(String meta2) {
        this.meta2 = meta2;
    }

    public String getMeta3() {
        return meta3;
    }

    public void setMeta3(String meta3) {
        this.meta3 = meta3;
    }

    public String getMeta4() {
        return meta4;
    }

    public void setMeta4(String meta4) {
        this.meta4 = meta4;
    }

    public String getMeta5() {
        return meta5;
    }

    public void setMeta5(String meta5) {
        this.meta5 = meta5;
    }

    public String getAnoMes() {
        return anoMes;
    }

    public void setAnoMes(String anoMes) {
        this.anoMes = anoMes;
    }

    public List<MetaFinanceiraConsultorDTO> getConsultores() {
        return consultores;
    }

    public void setConsultores(List<MetaFinanceiraConsultorDTO> consultores) {
        this.consultores = consultores;
    }
}
