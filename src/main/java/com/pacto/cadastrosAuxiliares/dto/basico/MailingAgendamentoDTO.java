package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "MailingAgendamentoDTO", description = "Configurações de agendamento para campanhas de mala direta")
public class MailingAgendamentoDTO {

    @Schema(description = "Código identificador do agendamento", example = "1")
    private Integer codigo;

    @Schema(description = "Código da mala direta associada", example = "5")
    protected Integer malaDireta;

    @Schema(description = "Número de ocorrências do agendamento", example = "3")
    protected Integer ocorrencia;

    @Schema(description = "Expressão CRON para agendamento automático", example = "0 9 * * MON")
    protected String cron;

    @Schema(description = "Data e hora da última execução", example = "2024-01-15T09:00:00")
    protected Date ultimaExecucao;

    @Schema(description = "Data inicial para início dos envios", example = "2024-01-10T09:00:00")
    protected Date dataInicial;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(Integer malaDireta) {
        this.malaDireta = malaDireta;
    }

    public Integer getOcorrencia() {
        return ocorrencia;
    }

    public void setOcorrencia(Integer ocorrencia) {
        this.ocorrencia = ocorrencia;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Date getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Date ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }
}
