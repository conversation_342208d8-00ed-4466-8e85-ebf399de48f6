package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
public class ImpostoProdutoCfopDTO {

    @Schema(description = "Código identificador único do imposto de produto", example = "1")
    private Integer codigo;

    @Schema(description = "Código Fiscal de Operações e Prestações utilizado para equipamentos de academia", example = "5102")
    protected String cfop;

    @Schema(description = "Nomenclatura Comum do Mercosul para equipamentos de fitness e academia", example = "95069900")
    protected String ncm;

    @Schema(description = "Código da configuração de Nota Fiscal de Serviço Eletrônica para serviços de academia", example = "1")
    private Integer configuracaoNfse;

    @Schema(description = "Código da configuração de Nota Fiscal do Consumidor Eletrônica para vendas de produtos de academia", example = "2")
    private Integer configuracaoNfce;

    @Schema(description = "Código da lista de serviços municipais para atividades de educação física", example = "17.09")
    private String codListaServico;

    @Schema(description = "Código de tributação municipal para serviços de academia e personal training", example = "170900")
    private String codTributacaoMunicipio;

    @Schema(description = "Descrição do serviço municipal para atividades de condicionamento físico", example = "Ensino de esportes, de lutas, de artes marciais e de atividades de condicionamento físico")
    private String desServicoMunicipio;

    @Schema(description = "Indica se deve enviar o percentual de imposto na nota fiscal de serviços de academia", example = "true")
    private boolean enviarPercentualImposto = true;

    @Schema(description = "Percentual de imposto federal aplicado sobre serviços de personal training", example = "3.0")
    private Double perFederal;

    @Schema(description = "Percentual de imposto estadual aplicado sobre equipamentos de academia", example = "18.0")
    private Double perEstadual;

    @Schema(description = "Percentual de imposto municipal aplicado sobre serviços de condicionamento físico", example = "5.0")
    private Double perMunicipal;

    @Schema(description = "Situação tributária do ICMS para equipamentos de academia", example = "00")
    private String situacaoTributariaICMS;

    @Schema(description = "Alíquota do ICMS aplicada sobre equipamentos de fitness", example = "18.0")
    private double aliquotaICMS = 0.0;

    @Schema(description = "Indica se equipamentos de academia são isentos de ICMS", example = "false")
    private boolean isentoICMS = false;

    @Schema(description = "Indica se deve enviar alíquota de ICMS na nota fiscal de equipamentos", example = "true")
    private boolean enviarAliquotaICMS = false;

    @Schema(description = "Situação tributária do PIS para serviços de personal training", example = "01")
    private String situacaoTributariaPIS;

    @Schema(description = "Alíquota do PIS aplicada sobre serviços de academia", example = "1.65")
    private double aliquotaPIS = 0.0;

    @Schema(description = "Indica se serviços de condicionamento físico são isentos de PIS", example = "false")
    private boolean isentoPIS = false;

    @Schema(description = "Indica se deve enviar alíquota de PIS na nota fiscal de serviços", example = "true")
    private boolean enviarAliquotaPIS = false;

    @Schema(description = "Situação tributária do COFINS para atividades de educação física", example = "01")
    private String situacaoTributariaCOFINS;

    @Schema(description = "Alíquota do COFINS aplicada sobre serviços de academia", example = "7.6")
    private double aliquotaCOFINS = 0.0;

    @Schema(description = "Indica se serviços de personal training são isentos de COFINS", example = "false")
    private boolean isentoCOFINS = false;

    @Schema(description = "Indica se deve enviar alíquota de COFINS na nota fiscal de serviços", example = "true")
    private boolean enviarAliquotaCOFINS = false;

    @Schema(description = "Código do Benefício Fiscal aplicado a equipamentos de academia", example = "PR870001")
    private String codBNEF;

    @Schema(description = "Indica se o imposto de produto está desativado no sistema da academia", example = "false")
    protected Boolean desativado;

    public ImpostoProdutoCfopDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCfop() {
        return cfop;
    }

    public void setCfop(String cfop) {
        this.cfop = cfop;
    }

    public String getNcm() {
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }



    public Integer getConfiguracaoNfse() {
        return configuracaoNfse;
    }

    public void setConfiguracaoNfse(Integer configuracaoNfse) {
        this.configuracaoNfse = configuracaoNfse;
    }

    public Integer getConfiguracaoNfce() {
        return configuracaoNfce;
    }

    public void setConfiguracaoNfce(Integer configuracaoNfce) {
        this.configuracaoNfce = configuracaoNfce;
    }

    public String getCodListaServico() {
        return codListaServico;
    }

    public void setCodListaServico(String codListaServico) {
        this.codListaServico = codListaServico;
    }

    public String getCodTributacaoMunicipio() {
        return codTributacaoMunicipio;
    }

    public void setCodTributacaoMunicipio(String codTributacaoMunicipio) {
        this.codTributacaoMunicipio = codTributacaoMunicipio;
    }

    public String getDesServicoMunicipio() {
        return desServicoMunicipio;
    }

    public void setDesServicoMunicipio(String desServicoMunicipio) {
        this.desServicoMunicipio = desServicoMunicipio;
    }

    public boolean isEnviarPercentualImposto() {
        return enviarPercentualImposto;
    }

    public void setEnviarPercentualImposto(boolean enviarPercentualImposto) {
        this.enviarPercentualImposto = enviarPercentualImposto;
    }

    public Double getPerFederal() {
        return perFederal;
    }

    public void setPerFederal(Double perFederal) {
        this.perFederal = perFederal;
    }

    public Double getPerEstadual() {
        return perEstadual;
    }

    public void setPerEstadual(Double perEstadual) {
        this.perEstadual = perEstadual;
    }

    public Double getPerMunicipal() {
        return perMunicipal;
    }

    public void setPerMunicipal(Double perMunicipal) {
        this.perMunicipal = perMunicipal;
    }


    public String getSituacaoTributariaPIS() {
        return situacaoTributariaPIS;
    }

    public void setSituacaoTributariaPIS(String situacaoTributariaPIS) {
        this.situacaoTributariaPIS = situacaoTributariaPIS;
    }

    public double getAliquotaPIS() {
        return aliquotaPIS;
    }

    public void setAliquotaPIS(double aliquotaPIS) {
        this.aliquotaPIS = aliquotaPIS;
    }

    public boolean isIsentoPIS() {
        return isentoPIS;
    }

    public void setIsentoPIS(boolean isentoPIS) {
        this.isentoPIS = isentoPIS;
    }

    public boolean isEnviarAliquotaPIS() {
        return enviarAliquotaPIS;
    }

    public void setEnviarAliquotaPIS(boolean enviarAliquotaPIS) {
        this.enviarAliquotaPIS = enviarAliquotaPIS;
    }

    public String getSituacaoTributariaCOFINS() {
        return situacaoTributariaCOFINS;
    }

    public void setSituacaoTributariaCOFINS(String situacaoTributariaCOFINS) {
        this.situacaoTributariaCOFINS = situacaoTributariaCOFINS;
    }

    public double getAliquotaCOFINS() {
        return aliquotaCOFINS;
    }

    public void setAliquotaCOFINS(double aliquotaCOFINS) {
        this.aliquotaCOFINS = aliquotaCOFINS;
    }

    public boolean isIsentoCOFINS() {
        return isentoCOFINS;
    }

    public void setIsentoCOFINS(boolean isentoCOFINS) {
        this.isentoCOFINS = isentoCOFINS;
    }

    public boolean isEnviarAliquotaCOFINS() {
        return enviarAliquotaCOFINS;
    }

    public void setEnviarAliquotaCOFINS(boolean enviarAliquotaCOFINS) {
        this.enviarAliquotaCOFINS = enviarAliquotaCOFINS;
    }

    public boolean isIsentoICMS() {
        return isentoICMS;
    }

    public void setIsentoICMS(boolean isentoICMS) {
        this.isentoICMS = isentoICMS;
    }

    public String getSituacaoTributariaICMS() {
        return situacaoTributariaICMS;
    }

    public void setSituacaoTributariaICMS(String situacaoTributariaICMS) {
        this.situacaoTributariaICMS = situacaoTributariaICMS;
    }

    public double getAliquotaICMS() {
        return aliquotaICMS;
    }

    public void setAliquotaICMS(double aliquotaICMS) {
        this.aliquotaICMS = aliquotaICMS;
    }

    public boolean isEnviarAliquotaICMS() {
        return enviarAliquotaICMS;
    }

    public void setEnviarAliquotaICMS(boolean enviarAliquotaICMS) {
        this.enviarAliquotaICMS = enviarAliquotaICMS;
    }

    public Boolean getDesativado() {
        return desativado;
    }

    public void setDesativado(Boolean desativado) {
        this.desativado = desativado;
    }

    public String getCodBNEF() {
        return codBNEF;
    }

    public void setCodBNEF(String codBNEF) {
        this.codBNEF = codBNEF;
    }
}
