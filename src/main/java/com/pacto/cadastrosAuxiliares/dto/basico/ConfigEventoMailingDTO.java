package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "ConfigEventoMailingDTO", description = "Configurações específicas de eventos para campanhas de mala direta")
public class ConfigEventoMailingDTO {
    @Schema(description = "Código identificador da configuração", example = "1")
    private Integer codigo;

    @Schema(description = "Código da mala direta associada", example = "5")
    protected Integer malaDireta;

    @Schema(description = "Código do evento configurado", example = "2")
    protected Integer evento;

    @Schema(description = "Data de início do período do evento", example = "2024-01-01T00:00:00")
    protected Date inicio;

    @Schema(description = "Data de fim do período do evento", example = "2024-01-31T23:59:59")
    protected Date fim;

    @Schema(description = "Número de ocorrências do evento", example = "3")
    protected Integer ocorrencia;

    @Schema(description = "Códigos de riscos separados por vírgula", example = "1,3,5")
    protected String codigosRiscos;

    @Schema(description = "Indica se número de faltas é maior que o especificado", example = "true")
    protected boolean nrFaltasmaior;

    @Schema(description = "Número de faltas do aluno", example = "5")
    protected Integer nrFaltas;

    @Schema(description = "Mínimo de dias vencidos para acionamento", example = "10")
    protected Integer minimoDiasVencido;

    @Schema(description = "Valor mínimo para filtro", example = "100")
    protected Integer minValor;

    @Schema(description = "Valor máximo para filtro", example = "500")
    protected Integer maxValor;

    @Schema(description = "Nomes de pendências separados por vírgula", example = "Mensalidade,Taxa")
    protected String nomesPendencias;

    @Schema(description = "Códigos de tipos de produtos separados por vírgula", example = "MA,RE,RN")
    protected String codigosTiposProdutos;

    @Schema(description = "Códigos de produtos de sessão separados por vírgula", example = "1,2,3")
    protected String codigosProdutoSessao;
    @Schema(description = "Códigos de colaboradores separados por vírgula", example = "10,15,20")
    protected String codigosColaboradores;

    @Schema(description = "Indica se inclui não clientes", example = "false")
    protected boolean naoClientes;

    @Schema(description = "Dias a mais para cálculo", example = "5")
    protected Integer dMais;

    @Schema(description = "Dias a menos para cálculo", example = "2")
    protected Integer dMenos;

    @Schema(description = "Quantidade mínima de parcelas vencidas", example = "2")
    protected Integer qtdMinParcelasVencidas;

    @Schema(description = "Quantidade de dias das parcelas vencidas", example = "30,60,90")
    protected String qtdDiasParcelasVencidas;

    @Schema(description = "Códigos de produtos vencidos separados por vírgula", example = "1,2,3")
    protected String codigosProdutosVencidos;

    @Schema(description = "Número de dias de início do FreePass", example = "7")
    protected Integer nrDiasInicioFreePass;

    @Schema(description = "Código de erro de remessa", example = "ERR001")
    protected String codigoErroRemessa;

    @Schema(description = "Dias para remessa", example = "3")
    protected Integer diasRemessa;

    @Schema(description = "Indica se boleto tem parcelas vencendo", example = "true")
    protected boolean boletoParcelasVencendo;

    @Schema(description = "Dias das parcelas vencendo", example = "15")
    protected Integer diasParcelasVencendo;

    @Schema(description = "Indica se usa modelo padrão de boleto", example = "true")
    protected boolean modeloPadraoBoleto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(Integer malaDireta) {
        this.malaDireta = malaDireta;
    }

    public Integer getEvento() {
        return evento;
    }

    public void setEvento(Integer evento) {
        this.evento = evento;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Integer getOcorrencia() {
        return ocorrencia;
    }

    public void setOcorrencia(Integer ocorrencia) {
        this.ocorrencia = ocorrencia;
    }

    public String getCodigosRiscos() {
        return codigosRiscos;
    }

    public void setCodigosRiscos(String codigosRiscos) {
        this.codigosRiscos = codigosRiscos;
    }

    public boolean isNrFaltasmaior() {
        return nrFaltasmaior;
    }

    public void setNrFaltasmaior(boolean nrFaltasmaior) {
        this.nrFaltasmaior = nrFaltasmaior;
    }

    public Integer getNrFaltas() {
        return nrFaltas;
    }

    public void setNrFaltas(Integer nrFaltas) {
        this.nrFaltas = nrFaltas;
    }

    public Integer getMinimoDiasVencido() {
        return minimoDiasVencido;
    }

    public void setMinimoDiasVencido(Integer minimoDiasVencido) {
        this.minimoDiasVencido = minimoDiasVencido;
    }

    public Integer getMinValor() {
        return minValor;
    }

    public void setMinValor(Integer minValor) {
        this.minValor = minValor;
    }

    public Integer getMaxValor() {
        return maxValor;
    }

    public void setMaxValor(Integer maxValor) {
        this.maxValor = maxValor;
    }

    public String getNomesPendencias() {
        return nomesPendencias;
    }

    public void setNomesPendencias(String nomesPendencias) {
        this.nomesPendencias = nomesPendencias;
    }

    public String getCodigosTiposProdutos() {
        return codigosTiposProdutos;
    }

    public void setCodigosTiposProdutos(String codigosTiposProdutos) {
        this.codigosTiposProdutos = codigosTiposProdutos;
    }

    public String getCodigosProdutoSessao() {
        return codigosProdutoSessao;
    }

    public void setCodigosProdutoSessao(String codigosProdutoSessao) {
        this.codigosProdutoSessao = codigosProdutoSessao;
    }

    public String getCodigosColaboradores() {
        return codigosColaboradores;
    }

    public void setCodigosColaboradores(String codigosColaboradores) {
        this.codigosColaboradores = codigosColaboradores;
    }

    public boolean isNaoClientes() {
        return naoClientes;
    }

    public void setNaoClientes(boolean naoClientes) {
        this.naoClientes = naoClientes;
    }

    public Integer getdMais() {
        return dMais;
    }

    public void setdMais(Integer dMais) {
        this.dMais = dMais;
    }

    public Integer getdMenos() {
        return dMenos;
    }

    public void setdMenos(Integer dMenos) {
        this.dMenos = dMenos;
    }

    public Integer getQtdMinParcelasVencidas() {
        return qtdMinParcelasVencidas;
    }

    public void setQtdMinParcelasVencidas(Integer qtdMinParcelasVencidas) {
        this.qtdMinParcelasVencidas = qtdMinParcelasVencidas;
    }

    public String getQtdDiasParcelasVencidas() {
        return qtdDiasParcelasVencidas;
    }

    public void setQtdDiasParcelasVencidas(String qtdDiasParcelasVencidas) {
        this.qtdDiasParcelasVencidas = qtdDiasParcelasVencidas;
    }

    public String getCodigosProdutosVencidos() {
        return codigosProdutosVencidos;
    }

    public void setCodigosProdutosVencidos(String codigosProdutosVencidos) {
        this.codigosProdutosVencidos = codigosProdutosVencidos;
    }

    public Integer getNrDiasInicioFreePass() {
        return nrDiasInicioFreePass;
    }

    public void setNrDiasInicioFreePass(Integer nrDiasInicioFreePass) {
        this.nrDiasInicioFreePass = nrDiasInicioFreePass;
    }

    public String getCodigoErroRemessa() {
        return codigoErroRemessa;
    }

    public void setCodigoErroRemessa(String codigoErroRemessa) {
        this.codigoErroRemessa = codigoErroRemessa;
    }

    public Integer getDiasRemessa() {
        return diasRemessa;
    }

    public void setDiasRemessa(Integer diasRemessa) {
        this.diasRemessa = diasRemessa;
    }

    public boolean isBoletoParcelasVencendo() {
        return boletoParcelasVencendo;
    }

    public void setBoletoParcelasVencendo(boolean boletoParcelasVencendo) {
        this.boletoParcelasVencendo = boletoParcelasVencendo;
    }

    public Integer getDiasParcelasVencendo() {
        return diasParcelasVencendo;
    }

    public void setDiasParcelasVencendo(Integer diasParcelasVencendo) {
        this.diasParcelasVencendo = diasParcelasVencendo;
    }

    public boolean isModeloPadraoBoleto() {
        return modeloPadraoBoleto;
    }

    public void setModeloPadraoBoleto(boolean modeloPadraoBoleto) {
        this.modeloPadraoBoleto = modeloPadraoBoleto;
    }
}
