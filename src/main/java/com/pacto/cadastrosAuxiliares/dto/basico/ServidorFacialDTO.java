package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@JsonInclude
@Schema(name = "Servidor Facial", description = "Informações de configuração de um servidor de reconhecimento facial para controle de acesso em academias")
public class ServidorFacialDTO {

    @Schema(description = "Código único identificador do servidor facial", example = "1")
    private Integer codigo;
    @Schema(description = "Descrição do servidor facial para identificação na academia", example = "Servidor Facial - Entrada Principal")
    private String descricao;
    @Schema(description = "Nome do computador onde o servidor facial está instalado", example = "SERVIDOR-ACADEMIA-01")
    private String nomeComputador;
    @Schema(description = "Configuração de resolução Full HD (0 = desabilitado, 1 = habilitado)", example = "0")
    private Integer fullHd;
    @Schema(description = "Largura interna de redimensionamento das imagens capturadas", example = "640")
    private Integer internalResizeWidth;
    @Schema(description = "Limite de detecção facial para reconhecimento de alunos e instrutores", example = "80")
    private Integer faceDetectionThreshold;
    @Schema(description = "Largura da prévia facial exibida no sistema", example = "96")
    private Integer facePreviewWidth;
    @Schema(description = "Tempo de pausa entre capturas de imagem em milissegundos", example = "1000")
    private Integer tempoSleep;
    @Schema(description = "Taxa de falso positivo aceitável para reconhecimento facial", example = "0.5")
    private Double falsoPositivo;
    @Schema(description = "Tempo mínimo entre reenvios de dados em milissegundos", example = "5000")
    private Integer tempoMinimoReenvio;
    @Schema(description = "Porta de comunicação do serviço de reconhecimento facial", example = "7348")
    private Integer portaReconhecimentoFacial;
    @Schema(description = "Configuração de envio de vídeo (0 = desabilitado, 1 = habilitado)", example = "0")
    private Integer enviarVideo;
    @Schema(description = "Configuração de terminal (0 = desabilitado, 1 = habilitado)", example = "0")
    private Integer terminal;
    @Schema(description = "Caminho da pasta onde são armazenadas as fotos para reconhecimento facial", example = "C:\\Academia\\ReconhecimentoFacial\\Fotos\\")
    private String pastaFotosReconhecimentoFacial;
    @Schema(description = "Caminho da pasta onde são armazenadas as fotos processadas", example = "C:\\Academia\\ReconhecimentoFacial\\FotosProcessadas\\")
    private String pastaFotosProcessadasReconhecimentoFacial;
    @Schema(description = "Caminho da pasta onde são armazenados os logs do sistema", example = "C:\\Academia\\ReconhecimentoFacial\\Logs\\")
    private String pastaLogsReconhecimentoFacial;
    @Schema(description = "Porta de comunicação do PC para reconhecimento facial", example = "7489")
    private Integer portaPcReconhecimentoFacial;
    @Schema(description = "Endereço do servidor de banco de dados facial", example = "*************")
    private String servidorBdFacial;
    @Schema(description = "Distância máxima para identificação facial em centímetros", example = "50")
    private Integer distanciaIdentificacao;
    @Schema(description = "Lista de câmeras associadas ao servidor facial")
    private List<CameraDTO> cameras;

    @Schema(description = "Empresa proprietária do servidor facial")
    private EmpresaDTO empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getNomeComputador() {
        return nomeComputador;
    }

    public void setNomeComputador(String nomeComputador) {
        this.nomeComputador = nomeComputador;
    }

    public Integer getFullHd() {
        return fullHd;
    }

    public void setFullHd(Integer fullHd) {
        this.fullHd = fullHd;
    }

    public Integer getInternalResizeWidth() {
        return internalResizeWidth;
    }

    public void setInternalResizeWidth(Integer internalResizeWidth) {
        this.internalResizeWidth = internalResizeWidth;
    }

    public Integer getFaceDetectionThreshold() {
        return faceDetectionThreshold;
    }

    public void setFaceDetectionThreshold(Integer faceDetectionThreshold) {
        this.faceDetectionThreshold = faceDetectionThreshold;
    }

    public Integer getFacePreviewWidth() {
        return facePreviewWidth;
    }

    public void setFacePreviewWidth(Integer facePreviewWidth) {
        this.facePreviewWidth = facePreviewWidth;
    }

    public Integer getTempoSleep() {
        return tempoSleep;
    }

    public void setTempoSleep(Integer tempoSleep) {
        this.tempoSleep = tempoSleep;
    }

    public Double getFalsoPositivo() {
        return falsoPositivo;
    }

    public void setFalsoPositivo(Double falsoPositivo) {
        this.falsoPositivo = falsoPositivo;
    }

    public Integer getTempoMinimoReenvio() {
        return tempoMinimoReenvio;
    }

    public void setTempoMinimoReenvio(Integer tempoMinimoReenvio) {
        this.tempoMinimoReenvio = tempoMinimoReenvio;
    }

    public Integer getPortaReconhecimentoFacial() {
        return portaReconhecimentoFacial;
    }

    public void setPortaReconhecimentoFacial(Integer portaReconhecimentoFacial) {
        this.portaReconhecimentoFacial = portaReconhecimentoFacial;
    }

    public Integer getEnviarVideo() {
        return enviarVideo;
    }

    public void setEnviarVideo(Integer enviarVideo) {
        this.enviarVideo = enviarVideo;
    }

    public Integer getTerminal() {
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    public String getPastaFotosReconhecimentoFacial() {
        return pastaFotosReconhecimentoFacial;
    }

    public void setPastaFotosReconhecimentoFacial(String pastaFotosReconhecimentoFacial) {
        this.pastaFotosReconhecimentoFacial = pastaFotosReconhecimentoFacial;
    }

    public String getPastaFotosProcessadasReconhecimentoFacial() {
        return pastaFotosProcessadasReconhecimentoFacial;
    }

    public void setPastaFotosProcessadasReconhecimentoFacial(String pastaFotosProcessadasReconhecimentoFacial) {
        this.pastaFotosProcessadasReconhecimentoFacial = pastaFotosProcessadasReconhecimentoFacial;
    }

    public String getPastaLogsReconhecimentoFacial() {
        return pastaLogsReconhecimentoFacial;
    }

    public void setPastaLogsReconhecimentoFacial(String pastaLogsReconhecimentoFacial) {
        this.pastaLogsReconhecimentoFacial = pastaLogsReconhecimentoFacial;
    }

    public Integer getPortaPcReconhecimentoFacial() {
        return portaPcReconhecimentoFacial;
    }

    public void setPortaPcReconhecimentoFacial(Integer portaPcReconhecimentoFacial) {
        this.portaPcReconhecimentoFacial = portaPcReconhecimentoFacial;
    }

    public String getServidorBdFacial() {
        return servidorBdFacial;
    }

    public void setServidorBdFacial(String servidorBdFacial) {
        this.servidorBdFacial = servidorBdFacial;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public Integer getDistanciaIdentificacao() {
        return distanciaIdentificacao;
    }

    public void setDistanciaIdentificacao(Integer distanciaIdentificacao) {
        this.distanciaIdentificacao = distanciaIdentificacao;
    }

    public List<CameraDTO> getCameras() {
        return cameras;
    }

    public void setCameras(List<CameraDTO> cameras) {
        this.cameras = cameras;
    }
}
