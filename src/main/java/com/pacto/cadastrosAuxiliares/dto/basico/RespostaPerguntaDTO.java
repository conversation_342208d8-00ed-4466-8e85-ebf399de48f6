package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.exceptions.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "RespostaPergunta", description = "Opção de resposta disponível para uma pergunta do questionário")
public class RespostaPerguntaDTO {
    @Schema(description = "Código identificador único da resposta", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição da opção de resposta para a pergunta do questionário", example = "Sim, possuo lesão no joelho direito")
    private String descricaorespota;

    @Schema(description = "Referência à pergunta à qual esta resposta pertence")
    private PerguntaDTO pergunta;

    @Schema(description = "Número da questão que define a ordem de apresentação da resposta", example = "1")
    private Integer nrQuestao;

    public RespostaPerguntaDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricaorespota() {
        return descricaorespota;
    }

    public void setDescricaorespota(String descricaorespota) {
        this.descricaorespota = descricaorespota;
    }

    public PerguntaDTO getPergunta() {
        return pergunta;
    }

    public void setPergunta(PerguntaDTO pergunta) {
        this.pergunta = pergunta;
    }

    public Integer getNrQuestao() {
        return nrQuestao;
    }

    public void setNrQuestao(Integer nrQuestao) {
        this.nrQuestao = nrQuestao;
    }
}
