package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "QuestionarioPergunta", description = "Associação entre questionário e pergunta com configurações específicas")
public class QuestionarioperguntaDTO {
    @Schema(description = "Código identificador único da associação questionário-pergunta", example = "8")
    private Integer codigo;

    @Schema(description = "Indica se a pergunta é obrigatória no questionário", example = "true")
    private Boolean obrigatoria;

    @Schema(description = "Referência ao questionário ao qual a pergunta pertence")
    private QuestionarioDTO questionario;

    @Schema(description = "Referência à pergunta associada ao questionário")
    private PerguntaDTO pergunta;

    @Schema(description = "Número da questão que define a ordem de apresentação no questionário", example = "3")
    private Integer nrQuestao;

    public QuestionarioperguntaDTO(){
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getObrigatoria() {
        if (obrigatoria == null){
            obrigatoria = false;
        }
        return obrigatoria;
    }

    public void setObrigatoria(Boolean obrigatoria) {
        this.obrigatoria = obrigatoria;
    }

    public QuestionarioDTO getQuestionario() {
        return questionario;
    }

    public void setQuestionario(QuestionarioDTO questionario) {
        this.questionario = questionario;
    }

    public PerguntaDTO getPergunta() {
        return pergunta;
    }

    public void setPergunta(PerguntaDTO pergunta) {
        this.pergunta = pergunta;
    }

    public Integer getNrQuestao() {
        return nrQuestao;
    }

    public void setNrQuestao(Integer nrQuestao) {
        this.nrQuestao = nrQuestao;
    }
}

