package com.pacto.cadastrosAuxiliares.dto.basico;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Grau de Instrução", description = "Informações do grau de instrução")
public class GrauInstrucaoDTO {
    @Schema(description = "Código único identificador do grau de instrução", example = "1")
    private Integer codigo;
    @Schema(description = "Descrição do grau de instrução", example = "Ensino Superior Completo")
    private String descricao;

    public GrauInstrucaoDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
