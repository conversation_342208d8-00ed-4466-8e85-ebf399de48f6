package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Meta Financeira Consultor", description = "Informações de personal trainer ou instrutor vinculado à meta financeira da academia")
public class MetaFinanceiraConsultorDTO {
    @Schema(description = "Código único identificador do vínculo consultor-meta", example = "1")
    private Integer codigo;
    @Schema(description = "Personal trainer ou instrutor da academia vinculado à meta")
    private ColaboradorDTO colaborador;
    @Schema(description = "Percentual de participação do personal trainer na meta financeira", example = "15.5")
    private Double percentagem;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColaboradorDTO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorDTO colaborador) {
        this.colaborador = colaborador;
    }

    public Double getPercentagem() {
        return percentagem;
    }

    public void setPercentagem(Double percentagem) {
        this.percentagem = percentagem;
    }
}
