package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.exceptions.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Tamanho de Armário", description = "Informações sobre os tamanhos de armários disponíveis nos vestiários da academia")
public class TamanhoArmarioDTO {
    @Schema(description = "Código identificador único do tamanho de armário", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição do tamanho do armário disponível no vestiário da academia", example = "Pequeno")
    private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public void validar() throws ServiceException {
        if(this.getDescricao() == null || this.getDescricao().isEmpty()) {
            throw new ServiceException("O campo DESCRICAO deve ser informado.");
        }
    }
}
