package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.cadastrosAuxiliares.entities.basico.Usuario;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Modelo de Contrato", description = "Informações de um modelo de contrato da academia")
public class ModeloContratoDTO {

    @Schema(description = "Código identificador único do modelo de contrato", example = "1")
    private Integer codigo;

    @Schema(description = "Data de definição do modelo de contrato em timestamp", example = "1672531200000")
    private Long dataDefinicao;

    @Schema(description = "Descrição do modelo de contrato", example = "Contrato de Plano Mensal Academia Fitness")
    private String descricao;

    private transient byte imagemLogo;

    @Schema(description = "Usuário responsável pela definição do modelo de contrato")
    private Usuario responsavelDefinicao;

    @Schema(description = "Nome do responsável pela definição do modelo de contrato", example = "João Silva")
    private String nomeResponsavel;

    @Schema(description = "Situação do modelo de contrato. \n\n" +
            "**Valores disponíveis**\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n", example = "AT")
    private String situacao;

    @Schema(description = "Texto completo do modelo de contrato com tags personalizáveis", example = "CONTRATO DE PRESTAÇÃO DE SERVIÇOS DE ACADEMIA\n\nO aluno [NOME_ALUNO] está contratando os serviços da academia...")
    private String texto;

    @Schema(description = "Tipo do contrato. \n\n" +
            "**Valores disponíveis**\n" +
            "- PL (PLANO)\n" +
            "- SE (SERVIÇO)\n" +
            "- CC (COMPROVANTE DE COMPRA)\n", example = "PL")
    private String tipoContrato;

    @Schema(description = "Indica se o modelo deve ser replicado para outras unidades da rede", example = "true")
    private Boolean replicar;

    public ModeloContratoDTO() { }

    public ModeloContratoDTO(Integer codigo,
                             Long dataDefinicao,
                             String descricao,
                             byte imagemLogo,
                             Usuario responsavelDefinicao,
                             String situacao,
                             String texto,
                             String tipoContrato) {
        this.codigo = codigo;
        this.dataDefinicao = dataDefinicao;
        this.descricao = descricao;
        this.imagemLogo = imagemLogo;
        this.responsavelDefinicao = responsavelDefinicao;
        this.situacao = situacao;
        this.texto = texto;
        this.tipoContrato = tipoContrato;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Long getDataDefinicao() {
        return dataDefinicao;
    }

    public void setDataDefinicao(Long dataDefinicao) {
        this.dataDefinicao = dataDefinicao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public byte getImagemLogo() {
        return imagemLogo;
    }

    public void setImagemLogo(byte imagemLogo) {
        this.imagemLogo = imagemLogo;
    }

    public Usuario getResponsavelDefinicao() {
        return responsavelDefinicao;
    }

    public void setResponsavelDefinicao(Usuario responsavelDefinicao) {
        this.responsavelDefinicao = responsavelDefinicao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public String getNomeResponsavel() {
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public ModeloContratoDTO clonar() {
        this.codigo = null;
        return this;
    }

    public Boolean getReplicar() {
        return replicar;
    }

    public void setReplicar(Boolean replicar) {
        this.replicar = replicar;
    }
}
