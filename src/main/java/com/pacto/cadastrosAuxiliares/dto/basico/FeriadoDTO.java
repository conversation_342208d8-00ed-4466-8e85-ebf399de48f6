package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "FeriadoDTO", description = "Dados de um feriado")
public class FeriadoDTO {
    @Schema(description = "Código identificador único do feriado", example = "1")
    private Integer codigo;

    @Schema(description = "Nome do feriado", example = "Natal")
    protected String descricao;

    @Schema(description = "Data específica do feriado (para feriados não recorrentes)", example = "2024-12-25")
    protected Date dia;

    @Schema(description = "Mês do feriado (para feriados recorrentes)", example = "12")
    protected String mes;

    @Schema(description = "Indica se é um feriado nacional", example = "true")
    protected Boolean nacional;

    @Schema(description = "Indica se é um feriado estadual", example = "false")
    protected Boolean estadual;

    @Schema(description = "Estado ao qual o feriado pertence (quando aplicável)")
    private EstadoDTO estado;

    @Schema(description = "Indica se é um feriado não recorrente (data específica)", example = "false")
    protected Boolean naoRecorrente;

    @Schema(description = "Cidade à qual o feriado pertence (quando aplicável)")
    private CidadeDTO cidade;

    @Schema(description = "País ao qual o feriado pertence")
    private PaisDTO pais;

    @Schema(description = "Item de feriado recorrente", example = "25/12")
    private String feriadoRecorrenteItem;

    @Schema(description = "Indica se o feriado deve ser replicado", example = "false")
    private boolean replicar;

    public FeriadoDTO() {
    }

    public FeriadoDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public FeriadoDTO clonar() {
        this.codigo = null;
        this.descricao = "Cópia de " + this.descricao;
        return this;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public Boolean getNacional() {
        return nacional;
    }

    public void setNacional(Boolean nacional) {
        this.nacional = nacional;
    }

    public Boolean getEstadual() {
        return estadual;
    }

    public void setEstadual(Boolean estadual) {
        this.estadual = estadual;
    }

    public EstadoDTO getEstado() {
        return estado;
    }

    public void setEstado(EstadoDTO estado) {
        this.estado = estado;
    }

    public Boolean getNaoRecorrente() {
        return naoRecorrente;
    }

    public void setNaoRecorrente(Boolean naoRecorrente) {
        this.naoRecorrente = naoRecorrente;
    }

    public CidadeDTO getCidade() {
        return cidade;
    }

    public void setCidade(CidadeDTO cidade) {
        this.cidade = cidade;
    }

    public PaisDTO getPais() {
        return pais;
    }

    public void setPais(PaisDTO pais) {
        this.pais = pais;
    }

    public String getFeriadoRecorrenteItem() {
        return feriadoRecorrenteItem;
    }

    public void setFeriadoRecorrenteItem(String feriadoRecorrenteItem) {
        this.feriadoRecorrenteItem = feriadoRecorrenteItem;
    }

    public boolean isReplicar() {
        return replicar;
    }

    public void setReplicar(boolean replicar) {
        this.replicar = replicar;
    }
}
