package com.pacto.cadastrosAuxiliares.dto.basico;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Grupo", description = "Informações de um grupo de alunos da academia")
public class GrupoDTO {
    @Schema(description = "Código único identificador do grupo", example = "15")
    protected Integer codigo;

    @Schema(description = "Nome descritivo do grupo de alunos", example = "Grupo Musculação Avançada")
    protected String descricao;

    @Schema(description = "Percentual de desconto aplicado para o grupo (em %)", example = "15.5")
    protected Double percentualDescontoGrupo;

    @Schema(description = "Valor fixo de desconto aplicado para o grupo (em reais)", example = "50.00")
    protected Double valorDescontoGrupo;

    @Schema(description = "Tipo de desconto aplicado ao grupo. \n\n" +
            "**Valores disponíveis**\n" +
            "- PERCENTUAL (Desconto em percentual)\n" +
            "- VALOR (Desconto em valor fixo)\n", example = "PERCENTUAL")
    protected String tipoDesconto;

    @Schema(description = "Quantidade mínima de alunos necessária para formar o grupo", example = "8")
    protected Integer quantidadeMinimaAluno;

    @Schema(description = "Situação dos alunos que podem participar do grupo. \n\n" +
            "**Valores disponíveis**\n" +
            "- ATIVO (Alunos ativos)\n" +
            "- INATIVO (Alunos inativos)\n" +
            "- TODOS (Todos os alunos)\n", example = "ATIVO")
    protected String situacaoAluno;

    @Schema(description = "Tipo de grupo de alunos. \n\n" +
            "**Valores disponíveis**\n" +
            "- FAMILIAR (Grupo familiar)\n" +
            "- CORPORATIVO (Grupo corporativo)\n" +
            "- MODALIDADE (Grupo por modalidade)\n" +
            "- PERSONAL (Grupo de personal training)\n", example = "MODALIDADE")
    private String tipo;

    @Schema(description = "Indica se o grupo está inativo no sistema", example = "false")
    private Boolean grupoInativo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getPercentualDescontoGrupo() {
        return percentualDescontoGrupo;
    }

    public void setPercentualDescontoGrupo(Double percentualDescontoGrupo) {
        this.percentualDescontoGrupo = percentualDescontoGrupo;
    }

    public Double getValorDescontoGrupo() {
        return valorDescontoGrupo;
    }

    public void setValorDescontoGrupo(Double valorDescontoGrupo) {
        this.valorDescontoGrupo = valorDescontoGrupo;
    }

    public String getTipoDesconto() {
        return tipoDesconto;
    }

    public void setTipoDesconto(String tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public Integer getQuantidadeMinimaAluno() {
        return quantidadeMinimaAluno;
    }

    public void setQuantidadeMinimaAluno(Integer quantidadeMinimaAluno) {
        this.quantidadeMinimaAluno = quantidadeMinimaAluno;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Boolean getGrupoInativo() {
        return grupoInativo;
    }

    public void setGrupoInativo(Boolean grupoInativo) {
        this.grupoInativo = grupoInativo;
    }
}
