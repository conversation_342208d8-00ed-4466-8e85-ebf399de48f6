package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Operadora de Cartão", description = "Informações de uma operadora de cartão para pagamentos de mensalidades e serviços da academia")
public class OperadoraCartaoDTO {
    @Schema(description = "Código único identificador da operadora de cartão", example = "15")
    private Integer codigo;
    @Schema(description = "Nome da operadora de cartão", example = "Visa Crédito Academia Fitness")
    private String descricao;
    @Schema(description = "Código identificador da operadora no sistema", example = "1001")
    private Integer codigoOperadora;
    @Schema(description = "Código de integração principal da operadora", example = "2001")
    private Integer codigoIntegracao;
    @Schema(description = "Código de integração APF da operadora", example = "3001")
    private Integer codigoIntegracaoAPF;
    @Schema(description = "Indica se a operadora aceita pagamentos a crédito", example = "true")
    private Boolean credito;
    @Schema(description = "Quantidade máxima de parcelas permitidas pela operadora", example = "12")
    private Integer qtdeMaxParcelas;
    @Schema(description = "Taxa de processamento da operadora em centavos", example = "350")
    private Integer taxa;
    @Schema(description = "Código de integração com a plataforma Vindi", example = "4001")
    private Integer codigoIntegracaoVindi;
    @Schema(description = "Código de integração com a plataforma Cielo", example = "5001")
    private Integer codigoIntegracaoCielo;
    @Schema(description = "Código de integração para débito", example = "6001")
    private Integer codigoIntegracaoDebito;
    @Schema(description = "Tipo de débito online da operadora", example = "1")
    private Integer tipoDebitoOnline;
    @Schema(description = "Código de integração com a plataforma E-Rede", example = "7001")
    private Integer codigoIntegracaoERede;
    @Schema(description = "Código de integração com a plataforma MaxiPago", example = "8001")
    private Integer codigoIntegracaoMaxiPago;
    @Schema(description = "Indica se a operadora está ativa para uso na academia", example = "true")
    private Boolean ativo;
    @Schema(description = "Código de integração com a plataforma FitnessCard", example = "9001")
    private Integer codigoIntegracaoFitnessCard;
    @Schema(description = "Código de integração com a plataforma GetNet", example = "10001")
    private Integer codigoIntegracaoGetNet;
    @Schema(description = "Código da bandeira Cappta", example = "11001")
    private Integer bandeiraCappta;
    @Schema(description = "Código das bandeiras GeoITD", example = "12001")
    private Integer bandeirasGeoITD;
    @Schema(description = "Indica se utiliza pinpad GeoITD", example = "false")
    private Boolean pinpadGeoITD;
    @Schema(description = "Código de integração com a plataforma MundiPagg", example = "13001")
    private Integer codigoIntegracaoMundiPagg;
    @Schema(description = "Código de integração com a plataforma Pagar.me", example = "14001")
    private Integer codigoIntegracaoPagarme;
    @Schema(description = "Indica se é a operadora padrão para recebimento de mensalidades", example = "false")
    private Boolean padraoRecebimento;
    @Schema(description = "Código de integração com a plataforma Stripe", example = "15001")
    private Integer codigoIntegracaoStripe;
    @Schema(description = "Código de integração com a plataforma PagoLivre", example = "16001")
    private Integer codigoIntegracaoPagoLivre;
    @Schema(description = "Código de integração com a plataforma Valor iBank", example = "17001")
    private Integer codigoIntegracaoValorIBank;
    @Schema(description = "Código de integração DCC Caixa Online", example = "18001")
    private Integer codigointegracaodcccaixaonline;

    public OperadoraCartaoDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigoOperadora() {
        return codigoOperadora;
    }

    public void setCodigoOperadora(Integer codigoOperadora) {
        this.codigoOperadora = codigoOperadora;
    }

    public Integer getCodigoIntegracao() {
        return codigoIntegracao;
    }

    public void setCodigoIntegracao(Integer codigoIntegracao) {
        this.codigoIntegracao = codigoIntegracao;
    }

    public Integer getCodigoIntegracaoAPF() {
        return codigoIntegracaoAPF;
    }

    public void setCodigoIntegracaoAPF(Integer codigoIntegracaoAPF) {
        this.codigoIntegracaoAPF = codigoIntegracaoAPF;
    }

    public Boolean getCredito() {
        return credito;
    }

    public void setCredito(Boolean credito) {
        this.credito = credito;
    }

    public Integer getQtdeMaxParcelas() {
        return qtdeMaxParcelas;
    }

    public void setQtdeMaxParcelas(Integer qtdeMaxParcelas) {
        this.qtdeMaxParcelas = qtdeMaxParcelas;
    }

    public Integer getTaxa() {
        return taxa;
    }

    public void setTaxa(Integer taxa) {
        this.taxa = taxa;
    }

    public Integer getCodigoIntegracaoVindi() {
        return codigoIntegracaoVindi;
    }

    public void setCodigoIntegracaoVindi(Integer codigoIntegracaoVindi) {
        this.codigoIntegracaoVindi = codigoIntegracaoVindi;
    }

    public Integer getCodigoIntegracaoCielo() {
        return codigoIntegracaoCielo;
    }

    public void setCodigoIntegracaoCielo(Integer codigoIntegracaoCielo) {
        this.codigoIntegracaoCielo = codigoIntegracaoCielo;
    }

    public Integer getCodigoIntegracaoDebito() {
        return codigoIntegracaoDebito;
    }

    public void setCodigoIntegracaoDebito(Integer codigoIntegracaoDebito) {
        this.codigoIntegracaoDebito = codigoIntegracaoDebito;
    }

    public Integer getTipoDebitoOnline() {
        return tipoDebitoOnline;
    }

    public void setTipoDebitoOnline(Integer tipoDebitoOnline) {
        this.tipoDebitoOnline = tipoDebitoOnline;
    }

    public Integer getCodigoIntegracaoERede() {
        return codigoIntegracaoERede;
    }

    public void setCodigoIntegracaoERede(Integer codigoIntegracaoERede) {
        this.codigoIntegracaoERede = codigoIntegracaoERede;
    }

    public Integer getCodigoIntegracaoMaxiPago() {
        return codigoIntegracaoMaxiPago;
    }

    public void setCodigoIntegracaoMaxiPago(Integer codigoIntegracaoMaxiPago) {
        this.codigoIntegracaoMaxiPago = codigoIntegracaoMaxiPago;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getCodigoIntegracaoFitnessCard() {
        return codigoIntegracaoFitnessCard;
    }

    public void setCodigoIntegracaoFitnessCard(Integer codigoIntegracaoFitnessCard) {
        this.codigoIntegracaoFitnessCard = codigoIntegracaoFitnessCard;
    }

    public Integer getCodigoIntegracaoGetNet() {
        return codigoIntegracaoGetNet;
    }

    public void setCodigoIntegracaoGetNet(Integer codigoIntegracaoGetNet) {
        this.codigoIntegracaoGetNet = codigoIntegracaoGetNet;
    }

    public Integer getBandeiraCappta() {
        return bandeiraCappta;
    }

    public void setBandeiraCappta(Integer bandeiraCappta) {
        this.bandeiraCappta = bandeiraCappta;
    }

    public Integer getBandeirasGeoITD() {
        return bandeirasGeoITD;
    }

    public void setBandeirasGeoITD(Integer bandeirasGeoITD) {
        this.bandeirasGeoITD = bandeirasGeoITD;
    }

    public Boolean getPinpadGeoITD() {
        return pinpadGeoITD;
    }

    public void setPinpadGeoITD(Boolean pinpadGeoITD) {
        this.pinpadGeoITD = pinpadGeoITD;
    }

    public Integer getCodigoIntegracaoMundiPagg() {
        return codigoIntegracaoMundiPagg;
    }

    public void setCodigoIntegracaoMundiPagg(Integer codigoIntegracaoMundiPagg) {
        this.codigoIntegracaoMundiPagg = codigoIntegracaoMundiPagg;
    }

    public Integer getCodigoIntegracaoPagarme() {
        return codigoIntegracaoPagarme;
    }

    public void setCodigoIntegracaoPagarme(Integer codigoIntegracaoPagarme) {
        this.codigoIntegracaoPagarme = codigoIntegracaoPagarme;
    }

    public Boolean getPadraoRecebimento() {
        return padraoRecebimento;
    }

    public void setPadraoRecebimento(Boolean padraoRecebimento) {
        this.padraoRecebimento = padraoRecebimento;
    }

    public Integer getCodigoIntegracaoStripe() {
        return codigoIntegracaoStripe;
    }

    public void setCodigoIntegracaoStripe(Integer codigoIntegracaoStripe) {
        this.codigoIntegracaoStripe = codigoIntegracaoStripe;
    }

    public Integer getCodigoIntegracaoPagoLivre() {
        return codigoIntegracaoPagoLivre;
    }

    public void setCodigoIntegracaoPagoLivre(Integer codigoIntegracaoPagoLivre) {
        this.codigoIntegracaoPagoLivre = codigoIntegracaoPagoLivre;
    }

    public Integer getCodigoIntegracaoValorIBank() {
        return codigoIntegracaoValorIBank;
    }

    public void setCodigoIntegracaoValorIBank(Integer codigoIntegracaoValorIBank) {
        this.codigoIntegracaoValorIBank = codigoIntegracaoValorIBank;
    }

    public Integer getCodigointegracaodcccaixaonline() {
        return codigointegracaodcccaixaonline;
    }

    public void setCodigointegracaodcccaixaonline(Integer codigointegracaodcccaixaonline) {
        this.codigointegracaodcccaixaonline = codigointegracaodcccaixaonline;
    }

}
