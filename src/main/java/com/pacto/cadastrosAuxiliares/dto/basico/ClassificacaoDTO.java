package com.pacto.cadastrosAuxiliares.dto.basico;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Classificação", description = "Informações de uma classificação")
public class ClassificacaoDTO {

    @Schema(description = "Código identificador único da classificação", example = "1")
    private Integer codigo;

    @Schema(description = "Nome da classificação", example = "Classificação Treino")
    private String nome;

    @Schema(description = "Indica se deve enviar SMS automaticamente para esta classificação", example = "true")
    private Boolean enviarsmsautomatico;

    public ClassificacaoDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getEnviarsmsautomatico() {
        return enviarsmsautomatico;
    }

    public void setEnviarsmsautomatico(Boolean enviarsmsautomatico) {
        this.enviarsmsautomatico = enviarsmsautomatico;
    }
}
