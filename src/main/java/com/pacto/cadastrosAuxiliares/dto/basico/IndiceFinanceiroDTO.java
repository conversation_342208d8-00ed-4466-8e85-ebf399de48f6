package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude
@Schema(name = "Índice Financeiro", description = "Dados de um índice financeiro para reajuste de preços de planos de academia")
public class IndiceFinanceiroDTO {
    @Schema(description = "Código identificador único do índice financeiro", example = "1")
    private Integer codigo;

    @Schema(description = "Ano de referência do índice financeiro", example = "2024")
    private String ano;

    @Schema(description = "Indica se o reajuste deve ser aplicado na renovação de contratos recorrentes de alunos da academia", example = "true")
    private Boolean aplicarReajusteRenovacaoContratoRecorrencia;

    @Schema(description = "Data de lançamento do índice financeiro no sistema da academia", example = "2024-01-15T10:30:00.000+00:00")
    private Date dataLancamento;

    @Schema(description = "Mês de referência do índice financeiro. \n\n" +
            "**Valores disponíveis**\n" +
            "- 01 (JANEIRO)\n" +
            "- 02 (FEVEREIRO)\n" +
            "- 03 (MARÇO)\n" +
            "- 04 (ABRIL)\n" +
            "- 05 (MAIO)\n" +
            "- 06 (JUNHO)\n" +
            "- 07 (JULHO)\n" +
            "- 08 (AGOSTO)\n" +
            "- 09 (SETEMBRO)\n" +
            "- 10 (OUTUBRO)\n" +
            "- 11 (NOVEMBRO)\n" +
            "- 12 (DEZEMBRO)\n", example = "01")
    private String mes;

    @Schema(description = "Percentual acumulado do índice financeiro aplicado aos preços dos planos de academia", example = "5.25")
    private Float percentualAcumulado;

    @Schema(description = "Tipo do índice financeiro utilizado para reajuste de preços. \n\n" +
            "**Valores disponíveis**\n" +
            "- 1 (IGPM)\n" +
            "- 2 (INPC)\n" +
            "- 3 (IPC_BR)\n" +
            "- 4 (IPCA)\n" +
            "- 5 (OUTROS)\n", example = "4")
    private Integer tipoIndice;

    @Schema(description = "Tipo de plano de academia ao qual o índice se aplica. \n\n" +
            "**Valores disponíveis**\n" +
            "- 0 (Plano Normal)\n" +
            "- 1 (Plano Recorrência)\n", example = "1")
    private Integer tipoPlano;

    public IndiceFinanceiroDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAno() {
        return ano;
    }

    public void setAno(String ano) {
        this.ano = ano;
    }

    public Boolean getAplicarReajusteRenovacaoContratoRecorrencia() {
        return aplicarReajusteRenovacaoContratoRecorrencia;
    }

    public void setAplicarReajusteRenovacaoContratoRecorrencia(Boolean aplicarReajusteRenovacaoContratoRecorrencia) {
        this.aplicarReajusteRenovacaoContratoRecorrencia = aplicarReajusteRenovacaoContratoRecorrencia;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public Float getPercentualAcumulado() {
        return percentualAcumulado;
    }

    public void setPercentualAcumulado(Float percentualAcumulado) {
        this.percentualAcumulado = percentualAcumulado;
    }

    public Integer getTipoIndice() {
        return tipoIndice;
    }

    public void setTipoIndice(Integer tipoIndice) {
        this.tipoIndice = tipoIndice;
    }

    public Integer getTipoPlano() {
        return tipoPlano;
    }

    public void setTipoPlano(Integer tipoPlano) {
        this.tipoPlano = tipoPlano;
    }
}
