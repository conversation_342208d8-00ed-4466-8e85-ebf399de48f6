package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.exceptions.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;


@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Pergun<PERSON>", description = "Informações de uma pergunta utilizada em questionários de avaliação física, saúde e satisfação da academia")
public class PerguntaDTO {
    @Schema(description = "Código identificador único da pergunta", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição da pergunta utilizada em questionários de avaliação física, saúde ou satisfação da academia", example = "Você possui alguma lesão ou limitação física que devemos considerar no seu treino?")
    private String descricao;

    @Schema(description = "Tipo da pergunta que define o formato de resposta esperado. \n\n" +
            "**Valores disponíveis**\n" +
            "- **SN**: Pergunta de Sim/Não (gera automaticamente as opções SIM e NÃO)\n" +
            "- **ME**: Múltipla Escolha (permite definir opções personalizadas de resposta)\n" +
            "- **TA**: Texto Aberto (permite resposta livre em formato texto)", example = "SN")
    private String tipopergunta;

    @Schema(description = "Lista de respostas possíveis para a pergunta. Para perguntas do tipo SN (Sim/Não), as respostas são geradas automaticamente")
    private List<RespostaPerguntaDTO> respotaPerguntas;


    public PerguntaDTO() {
    }

    public List<RespostaPerguntaDTO> getRespotaPerguntas() {
        if (respotaPerguntas == null) {
            return new ArrayList<>();
        }
        return respotaPerguntas;
    }

    public void setRespotaPerguntas(List<RespostaPerguntaDTO> respotaPerguntas) {
        this.respotaPerguntas = respotaPerguntas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipopergunta() {
        return tipopergunta;
    }

    public void setTipopergunta(String tipopergunta) {
        this.tipopergunta = tipopergunta;
    }


}
