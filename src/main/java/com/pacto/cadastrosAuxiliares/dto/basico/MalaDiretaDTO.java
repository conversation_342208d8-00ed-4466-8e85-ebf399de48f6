package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "MalaDiretaDTO", description = "Dados de uma campanha de mala direta para comunicação com alunos da academia")
public class MalaDiretaDTO {
    @Schema(description = "Código identificador único da campanha de mala direta", example = "1")
    private Integer codigo;

    @Schema(description = "Data e hora do envio da campanha", example = "2024-01-15T10:30:00")
    protected Date dataEnvio;

    @Schema(description = "Data e hora de criação da campanha", example = "2024-01-10T14:20:00")
    protected Date dataCriacao;

    @Schema(description = "Conteúdo da mensagem que será enviada aos alunos", example = "Venha treinar conosco! Novas modalidades disponíveis na academia.")
    protected String mensagem;

    @Schema(description = "Título da campanha de mala direta", example = "Promoção Janeiro - Novas Modalidades")
    protected String titulo;

    @Schema(description = "Código do remetente responsável pela campanha", example = "1")
    protected Integer remetente;

    @Schema(description = "Código do modelo de mensagem utilizado", example = "5")
    protected Integer modeloMensagem;

    @Schema(description = "Meio de envio da campanha. \n\n" +
            "**Valores disponíveis**\n" +
            "- 1 (E-mail)\n" +
            "- 2 (SMS)\n" +
            "- 3 (APP)\n" +
            "- 4 (CRM-EXTRA)\n" +
            "- 5 (FTP)\n" +
            "- 6 (WHATSAPP)\n", example = "1")
    protected Integer meioDeEnvio;

    @Schema(description = "Código da empresa/unidade da academia", example = "1")
    protected Integer empresa;

    @Schema(description = "Data limite de vigência da campanha", example = "2024-01-31T23:59:59")
    protected Date vigenteAte;

    @Schema(description = "Código do evento associado à campanha", example = "3")
    protected Integer evento;

    @Schema(description = "Query SQL personalizada para filtros avançados", example = "SELECT * FROM alunos WHERE situacao = 'ATIVO'")
    protected String sql = "";

    @Schema(description = "Tipo de agendamento da campanha. \n\n" +
            "**Valores disponíveis**\n" +
            "- 1 (Instantâneo)\n" +
            "- 2 (Agendamento)\n" +
            "- 3 (Todos)\n", example = "2")
    protected Integer tipoAgendamento;

    @Schema(description = "Indica se a campanha foi excluída", example = "false")
    protected boolean excluida;

    @Schema(description = "Indica se permite contato avulso", example = "true")
    protected boolean contatoavulso;
    @Schema(description = "Fase atual do envio da campanha", example = "PREPARACAO")
    protected String faseEnvio;

    @Schema(description = "Opções adicionais da campanha em formato JSON", example = "{\"incluirInativos\": false}")
    protected String opcoes;

    @Schema(description = "Código do tipo de pergunta associada", example = "2")
    protected Integer tipoPergunta;

    @Schema(description = "Código de abertura de meta relacionada", example = "10")
    protected Integer codAberturaMeta;

    @Schema(description = "Quantidade de dias após venda para envio", example = "7")
    protected Integer diasPosVenda;

    @Schema(description = "Indica se é meta extra individual", example = "false")
    protected boolean metaExtraIndividual;

    @Schema(description = "Tipo de consultor para meta extra individual", example = "PERSONAL_TRAINER")
    protected String tipoConsultorMetaExtraIndividual;

    @Schema(description = "Quantidade mínima de acessos do aluno", example = "5")
    protected Integer quantidadeMinimaAcessos;

    @Schema(description = "Quantidade máxima de acessos do aluno", example = "30")
    protected Integer quantidadeMaximaAcessos;

    @Schema(description = "Intervalo em dias entre envios", example = "15")
    protected Integer intervaloDias;

    @Schema(description = "Indica se aplica a todas as unidades da rede", example = "true")
    protected boolean todasEmpresas;

    @Schema(description = "Código do questionário associado", example = "3")
    protected Integer questionario;

    @Schema(description = "Código do tipo de cancelamento", example = "1")
    protected Integer tipoCancelamento;

    @Schema(description = "Indica se deve importar lista externa", example = "false")
    protected boolean importarLista;

    @Schema(description = "Configurações adicionais em formato JSON", example = "{\"autoResposta\": true}")
    protected String configs;
    @Schema(description = "Indica se o envio está habilitado", example = "true")
    protected boolean envioHabilitado;

    @Schema(description = "Indica se é campanha de SMS marketing", example = "false")
    protected boolean smsMarketing;

    @Schema(description = "Status de entregabilidade da campanha", example = "true")
    protected boolean statusEntregabilidade;

    @Schema(description = "Código do template utilizado", example = "12")
    protected Integer idTemplate;

    @Schema(description = "Indica se deve replicar a campanha", example = "false")
    protected boolean replicar;

    @Schema(description = "Configurações específicas do evento de mailing")
    protected ConfigEventoMailingDTO configEventoMailingDTO;

    @Schema(description = "Configurações de agendamento do mailing")
    protected MailingAgendamentoDTO mailingAgendamentoDTO;

    @Schema(description = "Filtros aplicados ao mailing")
    protected MailingFiltrosDTO mailingFiltrosDTO;

    public MalaDiretaDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public Integer getRemetente() {
        return remetente;
    }

    public void setRemetente(Integer remetente) {
        this.remetente = remetente;
    }

    public Integer getModeloMensagem() {
        return modeloMensagem;
    }

    public void setModeloMensagem(Integer modeloMensagem) {
        this.modeloMensagem = modeloMensagem;
    }

    public Integer getMeioDeEnvio() {
        return meioDeEnvio;
    }

    public void setMeioDeEnvio(Integer meioDeEnvio) {
        this.meioDeEnvio = meioDeEnvio;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getVigenteAte() {
        return vigenteAte;
    }

    public void setVigenteAte(Date vigenteAte) {
        this.vigenteAte = vigenteAte;
    }

    public Integer getEvento() {
        return evento;
    }

    public void setEvento(Integer evento) {
        this.evento = evento;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public Integer getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(Integer tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public boolean isExcluida() {
        return excluida;
    }

    public void setExcluida(boolean excluida) {
        this.excluida = excluida;
    }

    public boolean isContatoavulso() {
        return contatoavulso;
    }

    public void setContatoavulso(boolean contatoavulso) {
        this.contatoavulso = contatoavulso;
    }

    public String getFaseEnvio() {
        return faseEnvio;
    }

    public void setFaseEnvio(String faseEnvio) {
        this.faseEnvio = faseEnvio;
    }

    public String getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(String opcoes) {
        this.opcoes = opcoes;
    }

    public Integer getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(Integer tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }

    public Integer getCodAberturaMeta() {
        return codAberturaMeta;
    }

    public void setCodAberturaMeta(Integer codAberturaMeta) {
        this.codAberturaMeta = codAberturaMeta;
    }

    public Integer getDiasPosVenda() {
        return diasPosVenda;
    }

    public void setDiasPosVenda(Integer diasPosVenda) {
        this.diasPosVenda = diasPosVenda;
    }

    public boolean isMetaExtraIndividual() {
        return metaExtraIndividual;
    }

    public void setMetaExtraIndividual(boolean metaExtraIndividual) {
        this.metaExtraIndividual = metaExtraIndividual;
    }

    public String getTipoConsultorMetaExtraIndividual() {
        return tipoConsultorMetaExtraIndividual;
    }

    public void setTipoConsultorMetaExtraIndividual(String tipoConsultorMetaExtraIndividual) {
        this.tipoConsultorMetaExtraIndividual = tipoConsultorMetaExtraIndividual;
    }

    public Integer getQuantidadeMinimaAcessos() {
        return quantidadeMinimaAcessos;
    }

    public void setQuantidadeMinimaAcessos(Integer quantidadeMinimaAcessos) {
        this.quantidadeMinimaAcessos = quantidadeMinimaAcessos;
    }

    public Integer getQuantidadeMaximaAcessos() {
        return quantidadeMaximaAcessos;
    }

    public void setQuantidadeMaximaAcessos(Integer quantidadeMaximaAcessos) {
        this.quantidadeMaximaAcessos = quantidadeMaximaAcessos;
    }

    public Integer getIntervaloDias() {
        return intervaloDias;
    }

    public void setIntervaloDias(Integer intervaloDias) {
        this.intervaloDias = intervaloDias;
    }

    public boolean isTodasEmpresas() {
        return todasEmpresas;
    }

    public void setTodasEmpresas(boolean todasEmpresas) {
        this.todasEmpresas = todasEmpresas;
    }

    public Integer getQuestionario() {
        return questionario;
    }

    public void setQuestionario(Integer questionario) {
        this.questionario = questionario;
    }

    public Integer getTipoCancelamento() {
        return tipoCancelamento;
    }

    public void setTipoCancelamento(Integer tipoCancelamento) {
        this.tipoCancelamento = tipoCancelamento;
    }

    public boolean isImportarLista() {
        return importarLista;
    }

    public void setImportarLista(boolean importarLista) {
        this.importarLista = importarLista;
    }

    public String getConfigs() {
        return configs;
    }

    public void setConfigs(String configs) {
        this.configs = configs;
    }

    public boolean isEnvioHabilitado() {
        return envioHabilitado;
    }

    public void setEnvioHabilitado(boolean envioHabilitado) {
        this.envioHabilitado = envioHabilitado;
    }

    public boolean isSmsMarketing() {
        return smsMarketing;
    }

    public void setSmsMarketing(boolean smsMarketing) {
        this.smsMarketing = smsMarketing;
    }

    public boolean isStatusEntregabilidade() {
        return statusEntregabilidade;
    }

    public void setStatusEntregabilidade(boolean statusEntregabilidade) {
        this.statusEntregabilidade = statusEntregabilidade;
    }

    public Integer getIdTemplate() {
        return idTemplate;
    }

    public void setIdTemplate(Integer idTemplate) {
        this.idTemplate = idTemplate;
    }

    public boolean isReplicar() {
        return replicar;
    }

    public void setReplicar(boolean replicar) {
        this.replicar = replicar;
    }

    public ConfigEventoMailingDTO getConfigEventoMailingDTO() {
        return configEventoMailingDTO;
    }

    public void setConfigEventoMailingDTO(ConfigEventoMailingDTO configEventoMailingDTO) {
        this.configEventoMailingDTO = configEventoMailingDTO;
    }

    public MailingAgendamentoDTO getMailingAgendamentoDTO() {
        return mailingAgendamentoDTO;
    }

    public void setMailingAgendamentoDTO(MailingAgendamentoDTO mailingAgendamentoDTO) {
        this.mailingAgendamentoDTO = mailingAgendamentoDTO;
    }

    public MailingFiltrosDTO getMailingFiltrosDTO() {
        return mailingFiltrosDTO;
    }

    public void setMailingFiltrosDTO(MailingFiltrosDTO mailingFiltrosDTO) {
        this.mailingFiltrosDTO = mailingFiltrosDTO;
    }
}
