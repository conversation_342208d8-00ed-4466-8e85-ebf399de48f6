package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@JsonInclude
@Schema(name = "Cupom Fiscal", description = "Informações do cupom fiscal")
public class CupomFiscalDTO {

    @Schema(description = "Código único identificador do cupom fiscal", example = "1")
    private Integer codigo;
    @Schema(description = "Número do recibo associado ao cupom fiscal", example = "12345")
    private Integer recibo;
    @Schema(description = "Informações da pessoa associada ao cupom fiscal")
    private PessoaDTO pessoa;
    @Schema(description = "Código do responsável pela emissão do cupom fiscal", example = "10")
    private Integer responsavel;
    @Schema(description = "Código do local onde foi emitido o cupom fiscal", example = "5")
    private Integer local;
    @Schema(description = "Código do cupom associado", example = "789")
    private Integer co_cupom;
    @Schema(description = "Código do movimento de pagamento", example = "456")
    private Integer movpagamento;
    @Schema(description = "Código do cartão utilizado no pagamento", example = "123")
    private Integer cartao;
    @Schema(description = "Status de impressão do cupom fiscal. \n\n" +
            "**Valores disponíveis**\n" +
            "- **true**: Cupom fiscal impresso\n" +
            "- **false**: Cupom fiscal não impresso\n", example = "true")
    private Boolean statusImpressao;
    @Schema(description = "Valor total do cupom fiscal", example = "150.75")
    private Float valor;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.TIME)
    @Schema(description = "Data e hora do pagamento do cupom fiscal", example = "2024-01-15 14:30:00")
    private LocalDateTime dataPagamento;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.TIME)
    @Schema(description = "Data e hora da venda registrada no cupom fiscal", example = "2024-01-15 14:25:00")
    private LocalDateTime horaVenda;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.TIME)
    @Schema(description = "Data e hora da emissão do cupom fiscal", example = "2024-01-15 14:35:00")
    private LocalDateTime horaEmissao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public Integer getLocal() {
        return local;
    }

    public void setLocal(Integer local) {
        this.local = local;
    }

    public Integer getCo_cupom() {
        return co_cupom;
    }

    public void setCo_cupom(Integer co_cupom) {
        this.co_cupom = co_cupom;
    }

    public Integer getMovpagamento() {
        return movpagamento;
    }

    public void setMovpagamento(Integer movpagamento) {
        this.movpagamento = movpagamento;
    }

    public Integer getCartao() {
        return cartao;
    }

    public void setCartao(Integer cartao) {
        this.cartao = cartao;
    }

    public Boolean getStatusImpressao() {
        return statusImpressao;
    }

    public void setStatusImpressao(Boolean statusImpressao) {
        this.statusImpressao = statusImpressao;
    }

    public Float getValor() {
        return valor;
    }

    public void setValor(Float valor) {
        this.valor = valor;
    }

    public LocalDateTime getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(LocalDateTime dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public LocalDateTime getHoraVenda() {
        return horaVenda;
    }

    public void setHoraVenda(LocalDateTime horaVenda) {
        this.horaVenda = horaVenda;
    }

    public LocalDateTime getHoraEmissao() {
        return horaEmissao;
    }

    public void setHoraEmissao(LocalDateTime horaEmissao) {
        this.horaEmissao = horaEmissao;
    }
}
