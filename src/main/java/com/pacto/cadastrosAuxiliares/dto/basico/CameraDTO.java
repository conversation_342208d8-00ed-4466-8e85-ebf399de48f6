package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Câmera", description = "Informações de configuração de uma câmera de segurança para reconhecimento facial em academias")
public class CameraDTO {

    @Schema(description = "Código único identificador da câmera", example = "1")
    private Integer codigo;
    @Schema(description = "Descrição da câmera para identificação na academia", example = "Câmera Entrada Principal")
    private String descricao;
    @Schema(description = "Endereço IP da câmera na rede da academia", example = "************")
    private String endereco;
    @Schema(description = "Porta de comunicação da câmera", example = "80")
    private Integer porta;
    @Schema(description = "Configuração de terminal (0 = desabilitado, 1 = habilitado)", example = "0")
    private Integer terminal;
    @Schema(description = "Índice da câmera no sistema de reconhecimento facial", example = "1")
    private Integer indice;
    @Schema(description = "URL RTSP para streaming de vídeo da câmera", example = "rtsp://************:554/stream")
    private String urlRtsp;
    @Schema(description = "Código do servidor facial ao qual a câmera está associada", example = "1")
    private Integer servidorFacial;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public Integer getPorta() {
        return porta;
    }

    public void setPorta(Integer porta) {
        this.porta = porta;
    }

    public Integer getTerminal() {
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    public Integer getIndice() {
        return indice;
    }

    public void setIndice(Integer indice) {
        this.indice = indice;
    }

    public String getUrlRtsp() {
        return urlRtsp;
    }

    public void setUrlRtsp(String urlRtsp) {
        this.urlRtsp = urlRtsp;
    }

    public Integer getServidorFacial() {
        return servidorFacial;
    }

    public void setServidorFacial(Integer servidorFacial) {
        this.servidorFacial = servidorFacial;
    }
}
