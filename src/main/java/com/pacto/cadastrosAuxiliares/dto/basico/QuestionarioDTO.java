package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.mscomunication.midiams.dto.MidiaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Questionario", description = "Informações de um questionário de pesquisa de satisfação da academia")
public class QuestionarioDTO {
    @Schema(description = "Código identificador único do questionário", example = "15")
    protected Integer codigo;

    @Schema(description = "Nome interno do questionário para identificação no sistema", example = "Pesquisa Satisfação Equipamentos 2024")
    protected String nomeinterno;

    @Schema(description = "Tipo do questionário que define sua categoria de uso. \n\n" +
            "**Valores disponíveis**\n" +
            "- **PS**: Pesquisa de Satisfação (questionários para avaliar qualidade dos serviços da academia)\n" +
            "- **AF**: Avaliação Física (questionários para coleta de dados de saúde e condicionamento físico)\n" +
            "- **QS**: Questionário de Saúde (questionários médicos e de histórico de saúde dos alunos)", example = "PS")
    protected String tipoquestionario;

    @Schema(description = "Cor de fundo do questionário em formato hexadecimal", example = "#FFFFFF")
    protected String fundocor;

    @Schema(description = "Caminho da imagem de fundo do questionário", example = "/images/background-academia.jpg")
    protected String fundoimagem;

    @Schema(description = "Texto de introdução exibido no início do questionário", example = "Bem-vindo à nossa pesquisa de satisfação! Sua opinião é muito importante para melhorarmos nossos serviços.")
    protected String textoinicio;

    @Schema(description = "Texto de finalização exibido ao concluir o questionário", example = "Obrigado por participar! Suas respostas nos ajudarão a oferecer um melhor atendimento.")
    protected String textofim;

    @Schema(description = "Indica se o questionário permite apenas uma resposta por pessoa", example = "true")
    protected Boolean somenteumaresposta;

    @Schema(description = "Indica se o questionário está ativo e disponível para uso", example = "true")
    protected Boolean ativo;

    @Schema(description = "Título principal da pesquisa exibido aos participantes", example = "Pesquisa de Satisfação - Qualidade dos Equipamentos")
    protected String titulopesquisa;

    @Schema(description = "Lista de perguntas associadas ao questionário")
    private List<QuestionarioperguntaDTO> questionarioperguntas;

    @Schema(description = "Informações da mídia associada ao questionário")
    private MidiaDTO midia;

    @Schema(description = "URL da imagem do questionário", example = "https://academia.com/images/questionario-15.jpg")
    private String urlImagem;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeinterno() {
        return nomeinterno;
    }

    public void setNomeinterno(String nomeinterno) {
        this.nomeinterno = nomeinterno;
    }

    public String getTipoquestionario() {
        return tipoquestionario;
    }

    public void setTipoquestionario(String tipoquestionario) {
        this.tipoquestionario = tipoquestionario;
    }

    public String getFundocor() {
        return fundocor;
    }

    public void setFundocor(String fundocor) {
        this.fundocor = fundocor;
    }

    public String getTextoinicio() {
        return textoinicio;
    }

    public void setTextoinicio(String textoinicio) {
        this.textoinicio = textoinicio;
    }

    public String getTextofim() {
        return textofim;
    }

    public void setTextofim(String textofim) {
        this.textofim = textofim;
    }

    public Boolean getSomenteumaresposta() {
        return somenteumaresposta;
    }

    public void setSomenteumaresposta(Boolean somenteumaresposta) {
        this.somenteumaresposta = somenteumaresposta;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getTitulopesquisa() {
        return titulopesquisa;
    }

    public void setTitulopesquisa(String titulopesquisa) {
        this.titulopesquisa = titulopesquisa;
    }

    public List<QuestionarioperguntaDTO> getQuestionarioperguntas() {
        return questionarioperguntas;
    }

    public void setQuestionarioperguntas(List<QuestionarioperguntaDTO> questionarioperguntas) {
        this.questionarioperguntas = questionarioperguntas;
    }

    public String getFundoimagem() {
        return fundoimagem;
    }

    public void setFundoimagem(String fundoimagem) {
        this.fundoimagem = fundoimagem;
    }

    public MidiaDTO getMidia() {
        return midia;
    }

    public void setMidia(MidiaDTO midia) {
        this.midia = midia;
    }

    public String getUrlImagem() {
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }
}
