package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "MailingFiltrosDTO", description = "Filtros aplicados para segmentação de alunos em campanhas de mala direta")
public class MailingFiltrosDTO {

    @Schema(description = "Código identificador dos filtros", example = "1")
    private Integer codigo;

    @Schema(description = "Código da mala direta associada", example = "5")
    protected Integer malaDireta;

    @Schema(description = "Código da categoria de aluno", example = "2")
    protected Integer categoria;

    @Schema(description = "Situação do aluno na academia", example = "ATIVO")
    protected String situacao;

    @Schema(description = "Código do vínculo com colaborador", example = "10")
    protected Integer vinculoColaborador;

    @Schema(description = "Código da modalidade praticada", example = "3")
    protected Integer modalidade;

    @Schema(description = "Duração do plano em meses", example = "12")
    protected Integer duracao;

    @Schema(description = "Código do evento relacionado", example = "1")
    protected Integer evento;

    @Schema(description = "Códigos de categorias separados por vírgula", example = "1,2,3")
    protected String codigosCategoria;

    @Schema(description = "Códigos de modalidades separados por vírgula", example = "1,5,8")
    protected String codigosModalidades;
    @Schema(description = "Lista de situações separadas por vírgula", example = "ATIVO,VISITANTE")
    protected String listaSituacoes;

    @Schema(description = "Códigos de consultores separados por vírgula", example = "5,10,15")
    protected String codigosConsultores;

    @Schema(description = "Códigos de professores separados por vírgula", example = "2,7,12")
    protected String codigosProfessores;

    @Schema(description = "Códigos de planos separados por vírgula", example = "1,3,5")
    protected String codigosPlanos;

    @Schema(description = "Códigos de duração de contrato separados por vírgula", example = "6,12,24")
    protected String codigosContratoDuracao;

    @Schema(description = "Data mínima de cadastro do aluno", example = "2023-01-01T00:00:00")
    protected Date dataCadastroMin;

    @Schema(description = "Data máxima de cadastro do aluno", example = "2024-01-31T23:59:59")
    protected Date dataCadastroMax;

    @Schema(description = "Idade mínima do aluno", example = "18")
    protected Integer idadeMin;

    @Schema(description = "Idade máxima do aluno", example = "65")
    protected Integer idadeMax;

    @Schema(description = "Incluir alunos do sexo feminino", example = "true")
    protected boolean feminino;

    @Schema(description = "Incluir alunos do sexo masculino", example = "true")
    protected boolean masculino;

    @Schema(description = "Data mínima de vencimento do contrato", example = "2024-01-01T00:00:00")
    protected Date vencimentoContratoMin;

    @Schema(description = "Data máxima de vencimento do contrato", example = "2024-12-31T23:59:59")
    protected Date vencimentoContratoMax;

    @Schema(description = "Mínimo de dias sem comparecer à academia", example = "7")
    protected Integer diasSemComparecerMin;

    @Schema(description = "Máximo de dias sem comparecer à academia", example = "30")
    protected Integer diasSemComparecerMax;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(Integer malaDireta) {
        this.malaDireta = malaDireta;
    }

    public Integer getCategoria() {
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getVinculoColaborador() {
        return vinculoColaborador;
    }

    public void setVinculoColaborador(Integer vinculoColaborador) {
        this.vinculoColaborador = vinculoColaborador;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getEvento() {
        return evento;
    }

    public void setEvento(Integer evento) {
        this.evento = evento;
    }

    public String getCodigosCategoria() {
        return codigosCategoria;
    }

    public void setCodigosCategoria(String codigosCategoria) {
        this.codigosCategoria = codigosCategoria;
    }

    public String getCodigosModalidades() {
        return codigosModalidades;
    }

    public void setCodigosModalidades(String codigosModalidades) {
        this.codigosModalidades = codigosModalidades;
    }

    public String getListaSituacoes() {
        return listaSituacoes;
    }

    public void setListaSituacoes(String listaSituacoes) {
        this.listaSituacoes = listaSituacoes;
    }

    public String getCodigosConsultores() {
        return codigosConsultores;
    }

    public void setCodigosConsultores(String codigosConsultores) {
        this.codigosConsultores = codigosConsultores;
    }

    public String getCodigosProfessores() {
        return codigosProfessores;
    }

    public void setCodigosProfessores(String codigosProfessores) {
        this.codigosProfessores = codigosProfessores;
    }

    public String getCodigosPlanos() {
        return codigosPlanos;
    }

    public void setCodigosPlanos(String codigosPlanos) {
        this.codigosPlanos = codigosPlanos;
    }

    public String getCodigosContratoDuracao() {
        return codigosContratoDuracao;
    }

    public void setCodigosContratoDuracao(String codigosContratoDuracao) {
        this.codigosContratoDuracao = codigosContratoDuracao;
    }

    public Date getDataCadastroMin() {
        return dataCadastroMin;
    }

    public void setDataCadastroMin(Date dataCadastroMin) {
        this.dataCadastroMin = dataCadastroMin;
    }

    public Date getDataCadastroMax() {
        return dataCadastroMax;
    }

    public void setDataCadastroMax(Date dataCadastroMax) {
        this.dataCadastroMax = dataCadastroMax;
    }

    public Integer getIdadeMin() {
        return idadeMin;
    }

    public void setIdadeMin(Integer idadeMin) {
        this.idadeMin = idadeMin;
    }

    public Integer getIdadeMax() {
        return idadeMax;
    }

    public void setIdadeMax(Integer idadeMax) {
        this.idadeMax = idadeMax;
    }

    public boolean isFeminino() {
        return feminino;
    }

    public void setFeminino(boolean feminino) {
        this.feminino = feminino;
    }

    public boolean isMasculino() {
        return masculino;
    }

    public void setMasculino(boolean masculino) {
        this.masculino = masculino;
    }

    public Date getVencimentoContratoMin() {
        return vencimentoContratoMin;
    }

    public void setVencimentoContratoMin(Date vencimentoContratoMin) {
        this.vencimentoContratoMin = vencimentoContratoMin;
    }

    public Date getVencimentoContratoMax() {
        return vencimentoContratoMax;
    }

    public void setVencimentoContratoMax(Date vencimentoContratoMax) {
        this.vencimentoContratoMax = vencimentoContratoMax;
    }

    public Integer getDiasSemComparecerMin() {
        return diasSemComparecerMin;
    }

    public void setDiasSemComparecerMin(Integer diasSemComparecerMin) {
        this.diasSemComparecerMin = diasSemComparecerMin;
    }

    public Integer getDiasSemComparecerMax() {
        return diasSemComparecerMax;
    }

    public void setDiasSemComparecerMax(Integer diasSemComparecerMax) {
        this.diasSemComparecerMax = diasSemComparecerMax;
    }
}
