package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Categoria de Clientes", description = "Dados de uma categoria")
public class CategoriaDTO {

    @Schema(description = "Codigo identificador único da categoria", example = "1")
    protected Integer codigo;

    @Schema(description = "Nome da categoria", example = "Aluno")
    protected String nome;

    @Schema(description = "Tipo da categoria", example = "Aluno")
    protected String tipoCategoria;

    @Schema(description = "Nome externo da categoria", example = "Aluno")
    protected String nomeExterno;

    @Schema(description = "Indica se e obrigatorio informar CNPJ do cliente SESI", example = "true")
    protected Boolean obrigatorioCnpjClienteSesi;

    @Schema(description = "Indica se deve validar situacao da empresa SESI", example = "false")
    protected Boolean validarSituacaoEmpresaSesi;

    @Schema(description = "Codigo da clientela", example = "CLI001")
    protected String codigoClientela;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTipoCategoria() {
        return tipoCategoria;
    }

    public void setTipoCategoria(String tipoCategoria) {
        this.tipoCategoria = tipoCategoria;
    }

    public String getNomeExterno() {
        return nomeExterno;
    }

    public void setNomeExterno(String nomeExterno) {
        this.nomeExterno = nomeExterno;
    }

    public Boolean getObrigatorioCnpjClienteSesi() {
        return obrigatorioCnpjClienteSesi;
    }

    public void setObrigatorioCnpjClienteSesi(Boolean obrigatorioCnpjClienteSesi) {
        this.obrigatorioCnpjClienteSesi = obrigatorioCnpjClienteSesi;
    }

    public Boolean getValidarSituacaoEmpresaSesi() {
        return validarSituacaoEmpresaSesi;
    }

    public void setValidarSituacaoEmpresaSesi(Boolean validarSituacaoEmpresaSesi) {
        this.validarSituacaoEmpresaSesi = validarSituacaoEmpresaSesi;
    }

    public String getCodigoClientela() { return codigoClientela; }

    public void setCodigoClientela(String codigoClientela) { this.codigoClientela = codigoClientela; }
}
