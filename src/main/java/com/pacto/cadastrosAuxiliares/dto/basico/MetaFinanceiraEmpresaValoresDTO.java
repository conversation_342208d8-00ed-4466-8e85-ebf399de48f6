package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Meta Financeira Empresa Valores", description = "Valores específicos de uma meta financeira de academia")
public class MetaFinanceiraEmpresaValoresDTO {
    @Schema(description = "Código único identificador do valor da meta", example = "1")
    private Integer codigo;
    @Schema(description = "Valor monetário da meta de faturamento da academia", example = "15000.00")
    private Double valor;
    @Schema(description = "Cor de identificação visual da meta no sistema", example = "#28a745")
    private String cor;
    @Schema(description = "Observações específicas sobre este valor de meta", example = "Meta para aumento de matrículas de musculação")
    private String observacao;
    @Schema(description = "Código da meta financeira da academia à qual este valor pertence", example = "1")
    private Integer metaFinanceiraEmpresa;
    @Schema(description = "Descrição da meta", example = "Meta 1")
    private String meta;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public Integer getMetaFinanceiraEmpresa() {
        return metaFinanceiraEmpresa;
    }

    public void setMetaFinanceiraEmpresa(Integer metaFinanceiraEmpresa) {
        this.metaFinanceiraEmpresa = metaFinanceiraEmpresa;
    }
}
