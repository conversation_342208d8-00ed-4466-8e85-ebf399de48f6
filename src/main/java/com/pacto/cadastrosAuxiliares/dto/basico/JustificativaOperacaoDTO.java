package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Justificativa de Operação", description = "Informações de uma justificativa de operação de academia")
public class JustificativaOperacaoDTO {

    @Schema(description = "Código único identificador da justificativa de operação", example = "1")
    private Integer codigo;
    @Schema(description = "Descrição da justificativa de operação", example = "Atestado médico para suspensão temporária")
    private String descricao;
    @Schema(description = "Tipo de operação da justificativa. \n\n" +
            "**Valores disponíveis**\n" +
            "- AT (Atestado)\n" +
            "- CA (Cancelamento)\n" +
            "- CR (Férias)\n" +
            "- TR (Trancamento)\n" +
            "- BO (Bônus)\n", example = "AT")
    private String tipoOperacao;
    @Schema(description = "Nome da empresa associada à justificativa", example = "Academia Pacto - GO")
    private String nomeEmpresa;
    @Schema(description = "Informações da empresa associada à justificativa")
    private EmpresaDTO empresa;
    @Schema(description = "Indica se a justificativa isenta multa de cancelamento", example = "true")
    private Boolean isentarMultaCancelamento;
    @Schema(description = "Indica se é necessário anexar comprovante para esta justificativa", example = "true")
    private Boolean necessarioAnexarComprovante;
    @Schema(description = "Indica se a justificativa não cobra parcelas atrasadas no cancelamento", example = "false")
    private Boolean naoCobrarParcelasAtrasadasCancelamento;
    @Schema(description = "Indica se a justificativa está ativa", example = "true")
    private Boolean ativa;
    @Schema(description = "Descrição apresentável do tipo de operação", example = "Atestado")
    private String tipoOperacaoApresentar;
    @Schema(description = "Indica se a justificativa deve ser apresentada para todas as empresas", example = "false")
    private Boolean apresentarTodasEmpresas;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
        this.setTipoOperacaoApresentar(tipoOperacao);
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Boolean getIsentarMultaCancelamento() {
        return isentarMultaCancelamento;
    }

    public void setIsentarMultaCancelamento(Boolean isentarMultaCancelamento) {
        this.isentarMultaCancelamento = isentarMultaCancelamento;
    }

    public Boolean getNecessarioAnexarComprovante() {
        return necessarioAnexarComprovante;
    }

    public void setNecessarioAnexarComprovante(Boolean necessarioAnexarComprovante) {
        this.necessarioAnexarComprovante = necessarioAnexarComprovante;
    }

    public Boolean getNaoCobrarParcelasAtrasadasCancelamento() {
        return naoCobrarParcelasAtrasadasCancelamento;
    }

    public void setNaoCobrarParcelasAtrasadasCancelamento(Boolean naoCobrarParcelasAtrasadasCancelamento) {
        this.naoCobrarParcelasAtrasadasCancelamento = naoCobrarParcelasAtrasadasCancelamento;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public String getTipoOperacaoApresentar() {
        return this.tipoOperacaoApresentar;
    }

    public void setTipoOperacaoApresentar(String tipoOperacaoApresentar) {
        if (tipoOperacao == null) {
            this.tipoOperacaoApresentar = "";
        } else if (tipoOperacao.equals("AT")) {
            this.tipoOperacaoApresentar = "Atestado";
        } else if (tipoOperacao.equals("CA")) {
            this.tipoOperacaoApresentar = "Cancelamento";
        } else if (tipoOperacao.equals("CR")) {
            this.tipoOperacaoApresentar = "Férias";
        } else if (tipoOperacao.equals("TR")) {
            this.tipoOperacaoApresentar = "Trancamento";
        } else if (tipoOperacao.equals("BO")) {
            this.tipoOperacaoApresentar = "Bônus";
        } else {
            this.tipoOperacaoApresentar = tipoOperacaoApresentar;
        }
    }

    public Boolean getApresentarTodasEmpresas() {
        if (getEmpresa() == null){
            apresentarTodasEmpresas = true;
        }
        return apresentarTodasEmpresas;
    }

    public void setApresentarTodasEmpresas(Boolean apresentarTodasEmpresas) {
        this.apresentarTodasEmpresas = apresentarTodasEmpresas;
    }
}
