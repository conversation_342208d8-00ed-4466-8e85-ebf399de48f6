package com.pacto.cadastrosAuxiliares.dto.basico;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Profiss<PERSON>", description = "Dados de uma profissão para colaboradores e instrutores de academias")
public class ProfissaoDTO {
    @Schema(description = "Código identificador único da profissão", example = "1")
    private Integer codigo;

    @Schema(description = "Nome da profissão exercida na academia", example = "Educador Físico")
    private String descricao;

    public ProfissaoDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
