package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Conta Corrente", description = "Informações da conta corrente")
public class ContaCorrenteDTO {
    @Schema(description = "Código único identificador da conta corrente", example = "1")
    private Integer codigo;
    @Schema(description = "Número da agência bancária", example = "1234")
    private String agencia;
    @Schema(description = "Dígito verificador da agência", example = "5")
    private String agenciaDv;
    @Schema(description = "Número da conta corrente", example = "567890")
    private String contaCorrente;
    @Schema(description = "Dígito verificador da conta corrente", example = "1")
    private String contaCorrenteDv;
    @Schema(description = "Código da operação bancária", example = "013")
    private String codigoOperacao;
    @Schema(description = "Informações do banco associado à conta corrente")
    private BancoDTO banco;
    @Schema(description = "Nome do banco associado à conta corrente", example = "Banco do Brasil S.A.")
    private String nomeBanco;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getAgenciaDv() {
        return agenciaDv;
    }

    public void setAgenciaDv(String agenciaDv) {
        this.agenciaDv = agenciaDv;
    }

    public String getContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(String contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public String getContaCorrenteDv() {
        return contaCorrenteDv;
    }

    public void setContaCorrenteDv(String contaCorrenteDv) {
        this.contaCorrenteDv = contaCorrenteDv;
    }

    public String getCodigoOperacao() {
        return codigoOperacao;
    }

    public void setCodigoOperacao(String codigoOperacao) {
        this.codigoOperacao = codigoOperacao;
    }

    public BancoDTO getBanco() {
        return banco;
    }

    public void setBanco(BancoDTO banco) {
        this.banco = banco;
    }

    public String getNomeBanco() {
        return nomeBanco;
    }

    public void setNomeBanco(String nomeBanco) {
        this.nomeBanco = nomeBanco;
    }
}
