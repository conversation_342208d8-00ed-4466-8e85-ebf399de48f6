package com.pacto.cadastrosAuxiliares.dto.basico;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.exceptions.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Parentesco", description = "Informações de um tipo de parentesco para relacionamentos familiares em academias")
public class ParentescoDTO {
    @Schema(description = "Código único identificador do parentesco", example = "1")
    private Integer codigo;
    @Schema(description = "Idade limite para dependência do parentesco (usado para controle de dependentes em planos familiares)", example = "18")
    private Integer idadelimitedependencia;
    @Schema(description = "Descrição do tipo de parentesco", example = "Filho(a)")
    private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getIdadelimitedependencia() {
        return idadelimitedependencia;
    }

    public void setIdadelimitedependencia(Integer idadelimitedependencia) {
        this.idadelimitedependencia = idadelimitedependencia;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
