package com.pacto.cadastrosAuxiliares.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LogAlteracoesDTO {

    @Schema(description = "Nome do campo que foi alterado", example = "aliquotaICMS")
    private String campo;

    @Schema(description = "Valor anterior do campo antes da alteração", example = "15.0")
    private String valorAnterior;

    @Schema(description = "Novo valor do campo após a alteração", example = "18.0")
    private String valorAlterado;

    public String getCampo() {
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getValorAnterior() {
        return valorAnterior;
    }

    public void setValorAnterior(String valorAnterior) {
        this.valorAnterior = valorAnterior;
    }

    public String getValorAlterado() {
        return valorAlterado;
    }

    public void setValorAlterado(String valorAlterado) {
        this.valorAlterado = valorAlterado;
    }
}
