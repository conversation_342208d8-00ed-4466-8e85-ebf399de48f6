package com.pacto.cadastrosAuxiliares.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LogDTO {

    @Schema(description = "Código identificador único do log", example = "1")
    private Integer codigo;

    @Schema(description = "Nome da entidade que foi alterada no sistema da academia", example = "ImpostoProdutoCfop")
    private String nomeEntidade;

    @Schema(description = "Descrição da entidade alterada", example = "Imposto de Produto CFOP")
    private String nomeEntidadeDescricao;

    @Schema(description = "Chave primária do registro alterado", example = "1")
    private String chavePrimaria;

    @Schema(description = "Chave primária da entidade subordinada", example = "2")
    private String chavePrimariaEntidadeSubordinada;

    @Schema(description = "Nome do campo que foi alterado", example = "aliquotaICMS")
    private String nomeCampo;

    @Schema(description = "Valor anterior do campo antes da alteração", example = "15.0")
    private String valorCampoAnterior;

    @Schema(description = "Novo valor do campo após a alteração", example = "18.0")
    private String valorCampoAlterado;

    @Schema(description = "Data e hora da alteração", example = "2024-01-15T10:30:00")
    private Date dataAlteracao;

    @Schema(description = "Usuário responsável pela alteração no sistema", example = "admin.academia")
    private String responsavelAlteracao;

    @Schema(description = "Tipo de operação realizada", example = "ALTERAÇÃO")
    private String operacao;

    @Schema(description = "Código da pessoa relacionada à alteração", example = "123")
    private Integer pessoa;

    @Schema(description = "Código do cliente relacionado à alteração", example = "456")
    private Integer cliente;

    @Schema(description = "Descrição detalhada da alteração realizada", example = "Alteração na alíquota de ICMS para equipamentos de academia")
    private String descricao;

    // Campos da nova estrutura de log
    // Para manter a compatibilidade dos locais que utilizam a consulta por log antiga será mantido o mesmo
    @Schema(description = "Chave identificadora do registro alterado", example = "1")
    private String chave;

    @Schema(description = "Nome do usuário que realizou a alteração", example = "admin.academia")
    private String usuario;

    @Schema(description = "Data da alteração no formato dd/MM/yyyy", example = "15/01/2024")
    private String dia;

    @Schema(description = "Hora da alteração no formato HH:mm:ss", example = "10:30:00")
    private String hora;

    @Schema(description = "Identificador da entidade alterada", example = "Imposto de Produto CFOP")
    private String identificador;

    @Schema(description = "Lista de alterações realizadas no registro")
    private List<LogAlteracoesDTO> alteracoes;

    @Schema(description = "Origem da alteração no sistema", example = "WEB")
    private String origem;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    public String getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public String getChavePrimariaEntidadeSubordinada() {
        return chavePrimariaEntidadeSubordinada;
    }

    public void setChavePrimariaEntidadeSubordinada(String chavePrimariaEntidadeSubordinada) {
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public void setNomeCampo(String nomeCampo) {
        this.nomeCampo = nomeCampo;
    }

    public String getValorCampoAnterior() {
        return valorCampoAnterior;
    }

    public void setValorCampoAnterior(String valorCampoAnterior) {
        this.valorCampoAnterior = valorCampoAnterior;
    }

    public String getValorCampoAlterado() {
        return valorCampoAlterado;
    }

    public void setValorCampoAlterado(String valorCampoAlterado) {
        this.valorCampoAlterado = valorCampoAlterado;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public String getResponsavelAlteracao() {
        return responsavelAlteracao;
    }

    public void setResponsavelAlteracao(String responsavelAlteracao) {
        this.responsavelAlteracao = responsavelAlteracao;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public List<LogAlteracoesDTO> getAlteracoes() {
        return alteracoes;
    }

    public void setAlteracoes(List<LogAlteracoesDTO> alteracoes) {
        this.alteracoes = alteracoes;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
}
