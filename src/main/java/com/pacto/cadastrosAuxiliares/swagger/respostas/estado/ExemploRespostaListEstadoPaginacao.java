package com.pacto.cadastrosAuxiliares.swagger.respostas.estado;

import com.pacto.cadastrosAuxiliares.dto.basico.EstadoDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Estados Paginada", description = "Exemplo da resposta contendo uma lista paginada de estados")
public class ExemploRespostaListEstadoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de estados encontrados na consulta")
    private List<EstadoDTO> content;

    public List<EstadoDTO> getContent() {
        return content;
    }

    public void setContent(List<EstadoDTO> content) {
        this.content = content;
    }
}
