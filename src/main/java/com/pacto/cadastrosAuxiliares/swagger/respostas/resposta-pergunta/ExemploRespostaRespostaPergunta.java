package com.pacto.cadastrosAuxiliares.swagger.respostas.respostapergunta;

import com.pacto.cadastrosAuxiliares.dto.basico.RespostaPerguntaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Resposta de Pergunta", description = "Exemplo da resposta contendo as informações de uma opção de resposta específica para pergunta de questionário")
public class ExemploRespostaRespostaPergunta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private RespostaPerguntaDTO content;

    public RespostaPerguntaDTO getContent() {
        return content;
    }

    public void setContent(RespostaPerguntaDTO content) {
        this.content = content;
    }
}
