package com.pacto.cadastrosAuxiliares.swagger.respostas.pais;

import com.pacto.cadastrosAuxiliares.dto.basico.PaisDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista Simplificada de Países", description = "Exemplo da resposta contendo uma lista simplificada de países com código e nome")
public class ExemploRespostaListPaisMin {

    @Schema(description = "Lista de países encontrados na consulta")
    private List<PaisDTO> content;

    public List<PaisDTO> getContent() {
        return content;
    }

    public void setContent(List<PaisDTO> content) {
        this.content = content;
    }
}
