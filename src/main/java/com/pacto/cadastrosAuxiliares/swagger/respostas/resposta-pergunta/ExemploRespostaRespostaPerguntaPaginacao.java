package com.pacto.cadastrosAuxiliares.swagger.respostas.respostapergunta;

import com.pacto.cadastrosAuxiliares.dto.basico.RespostaPerguntaDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Respostas de Pergunta Paginada", description = "Exemplo da resposta contendo uma lista paginada de opções de resposta para perguntas de questionários de academias")
public class ExemploRespostaRespostaPerguntaPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de opções de resposta encontradas na consulta")
    private List<RespostaPerguntaDTO> content;

    public List<RespostaPerguntaDTO> getContent() {
        return content;
    }

    public void setContent(List<RespostaPerguntaDTO> content) {
        this.content = content;
    }
}
