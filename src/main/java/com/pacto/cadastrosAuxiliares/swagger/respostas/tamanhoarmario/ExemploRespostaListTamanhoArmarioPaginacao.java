package com.pacto.cadastrosAuxiliares.swagger.respostas.tamanhoarmario;

import com.pacto.cadastrosAuxiliares.dto.basico.TamanhoArmarioDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Tamanhos de Armário Paginada", description = "Exemplo da resposta contendo uma lista paginada de tamanhos de armários para vestiários de academias")
public class ExemploRespostaListTamanhoArmarioPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de tamanhos de armários encontrados na consulta")
    private List<TamanhoArmarioDTO> content;

    public List<TamanhoArmarioDTO> getContent() {
        return content;
    }

    public void setContent(List<TamanhoArmarioDTO> content) {
        this.content = content;
    }
}
