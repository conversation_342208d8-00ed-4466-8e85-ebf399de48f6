package com.pacto.cadastrosAuxiliares.swagger.respostas.metafinanceira;

import com.pacto.cadastrosAuxiliares.dto.basico.MetaFinanceiraEmpresaDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Metas Financeiras com Paginação", description = "Exemplo da resposta contendo uma lista paginada de metas financeiras de academias")
public class ExemploRespostaListMetaFinanceiraEmpresaPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<MetaFinanceiraEmpresaDTO> content;

    public List<MetaFinanceiraEmpresaDTO> getContent() {
        return content;
    }

    public void setContent(List<MetaFinanceiraEmpresaDTO> content) {
        this.content = content;
    }
}
