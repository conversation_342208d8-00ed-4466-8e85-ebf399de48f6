package com.pacto.cadastrosAuxiliares.swagger.respostas.pesquisasatisfacao;

import com.pacto.cadastrosAuxiliares.dto.basico.QuestionarioDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Pesquisas de Satisfação Paginada", description = "Exemplo da resposta contendo uma lista paginada de pesquisas de satisfação da academia")
public class ExemploRespostaPesquisaSatisfacaoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de pesquisas de satisfação encontradas na consulta")
    private List<QuestionarioDTO> content;

    public List<QuestionarioDTO> getContent() {
        return content;
    }

    public void setContent(List<QuestionarioDTO> content) {
        this.content = content;
    }
}
