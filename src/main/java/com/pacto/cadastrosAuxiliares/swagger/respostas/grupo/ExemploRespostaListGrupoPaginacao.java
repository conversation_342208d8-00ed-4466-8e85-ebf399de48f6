package com.pacto.cadastrosAuxiliares.swagger.respostas.grupo;

import com.pacto.cadastrosAuxiliares.dto.basico.GrupoDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Grupos", description = "Exemplo da resposta contendo uma lista paginada de grupos de alunos")
public class ExemploRespostaListGrupoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<GrupoDTO> content;

    public List<GrupoDTO> getContent() {
        return content;
    }

    public void setContent(List<GrupoDTO> content) {
        this.content = content;
    }
}
