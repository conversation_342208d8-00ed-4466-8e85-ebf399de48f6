package com.pacto.cadastrosAuxiliares.swagger.respostas.erros;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Erro de Requisição", description = "Exemplo da resposta para erros de validação ou dados inválidos na requisição")
public class ExemploRespostaErroBadRequest {

    @Schema(description = "Chave identificadora do erro para localização e tratamento específico", example = "profissao.validar")
    private String chaveExcecao;

    @Schema(description = "Mensagem descritiva do erro para orientar o usuário sobre o problema encontrado", example = "O campo DESCRIÇÃO deve ser informado.")
    private String mensagem;

    @Schema(description = "Código de status HTTP da resposta", example = "400")
    private Integer status;

    @Schema(description = "Timestamp do momento em que o erro ocorreu", example = "2024-07-03T10:30:00.000Z")
    private String timestamp;

    public String getChaveExcecao() {
        return chaveExcecao;
    }

    public void setChaveExcecao(String chaveExcecao) {
        this.chaveExcecao = chaveExcecao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
}
