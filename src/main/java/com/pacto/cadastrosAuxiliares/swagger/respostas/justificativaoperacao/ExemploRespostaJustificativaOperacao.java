package com.pacto.cadastrosAuxiliares.swagger.respostas.justificativaoperacao;

import com.pacto.cadastrosAuxiliares.dto.basico.JustificativaOperacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta Justificativa de Operação", description = "Exemplo de resposta para consulta de uma justificativa de operação")
public class ExemploRespostaJustificativaOperacao {

    @Schema(description = "Dados da justificativa de operação consultada")
    private JustificativaOperacaoDTO content;

    public JustificativaOperacaoDTO getContent() {
        return content;
    }

    public void setContent(JustificativaOperacaoDTO content) {
        this.content = content;
    }
}
