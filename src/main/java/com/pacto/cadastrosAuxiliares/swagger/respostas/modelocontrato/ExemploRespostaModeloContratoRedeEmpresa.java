package com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato;

import com.pacto.cadastrosAuxiliares.entities.basico.ModeloContratoRedeEmpresa;
import io.swagger.v3.oas.annotations.media.Schema;

public class ExemploRespostaModeloContratoRedeEmpresa {

    @Schema(description = "Conteúdo da resposta contendo os dados do modelo de contrato rede empresa")
    private ModeloContratoRedeEmpresa content;

    public ModeloContratoRedeEmpresa getContent() {
        return content;
    }

    public void setContent(ModeloContratoRedeEmpresa content) {
        this.content = content;
    }
}
