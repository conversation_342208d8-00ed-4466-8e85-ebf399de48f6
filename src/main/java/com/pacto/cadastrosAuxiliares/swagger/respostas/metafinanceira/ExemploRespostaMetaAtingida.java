package com.pacto.cadastrosAuxiliares.swagger.respostas.metafinanceira;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Meta Atingida", description = "Exemplo da resposta contendo o valor da meta financeira atingida pela academia")
public class ExemploRespostaMetaAtingida {

    @Schema(description = "Valor da meta financeira atingida pela academia", example = "22500.80")
    private Double content;

    public Double getContent() {
        return content;
    }

    public void setContent(Double content) {
        this.content = content;
    }
}
