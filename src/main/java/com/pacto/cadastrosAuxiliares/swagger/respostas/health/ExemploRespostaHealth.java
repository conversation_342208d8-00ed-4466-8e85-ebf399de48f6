package com.pacto.cadastrosAuxiliares.swagger.respostas.health;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Health", description = "Exemplo da resposta contendo o status de saúde do sistema de gestão da academia")
public class ExemploRespostaHealth {

    @Schema(description = "Conteúdo da resposta contendo o status de saúde do sistema", example = "it's alive! v2")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
