package com.pacto.cadastrosAuxiliares.swagger.respostas.grupo;

import com.pacto.cadastrosAuxiliares.dto.basico.GrupoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Grupo", description = "Exemplo da resposta contendo as informações de um grupo de alunos")
public class ExemploRespostaGrupo {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private GrupoDTO content;

    public GrupoDTO getContent() {
        return content;
    }

    public void setContent(GrupoDTO content) {
        this.content = content;
    }
}
