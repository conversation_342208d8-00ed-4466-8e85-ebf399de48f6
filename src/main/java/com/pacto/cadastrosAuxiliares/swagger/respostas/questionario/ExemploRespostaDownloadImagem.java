package com.pacto.cadastrosAuxiliares.swagger.respostas.questionario;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Download da Imagem", description = "Exemplo da resposta contendo os dados binários da imagem do questionário")
public class ExemploRespostaDownloadImagem {

    @Schema(description = "Array de bytes contendo os dados binários da imagem do questionário para download")
    private byte[] content;

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }
}
