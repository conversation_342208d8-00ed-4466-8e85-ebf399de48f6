package com.pacto.cadastrosAuxiliares.swagger.respostas.empresa;

import com.pacto.cadastrosAuxiliares.dto.basico.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Empresa", description = "Exemplo da resposta contendo as informações de uma empresa")
public class ExemploRespostaEmpresa {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private EmpresaDTO content;

    public EmpresaDTO getContent() {
        return content;
    }

    public void setContent(EmpresaDTO content) {
        this.content = content;
    }
}
