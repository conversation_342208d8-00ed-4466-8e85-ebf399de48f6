package com.pacto.cadastrosAuxiliares.swagger.respostas.maladiretas;

import com.pacto.cadastrosAuxiliares.dto.basico.MalaDiretaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Mala Direta", description = "Exemplo da resposta contendo as informações de uma campanha de mala direta")
public class ExemploRespostaMalaDireta {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private MalaDiretaDTO content;

    public MalaDiretaDTO getContent() {
        return content;
    }

    public void setContent(MalaDiretaDTO content) {
        this.content = content;
    }
}
