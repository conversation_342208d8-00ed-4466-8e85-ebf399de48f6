package com.pacto.cadastrosAuxiliares.swagger.respostas.questionariopergunta;

import com.pacto.cadastrosAuxiliares.dto.basico.QuestionarioperguntaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Associações Questionário-Pergunta", description = "Exemplo da resposta contendo uma lista de associações entre questionários e perguntas configuradas para avaliações físicas e pesquisas de satisfação da academia")
public class ExemploRespostaListQuestionarioPergunta {

    @Schema(description = "Lista de associações entre questionários e perguntas com suas configurações específicas")
    private List<QuestionarioperguntaDTO> content;

    public List<QuestionarioperguntaDTO> getContent() {
        return content;
    }

    public void setContent(List<QuestionarioperguntaDTO> content) {
        this.content = content;
    }
}
