package com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao;

import com.pacto.cadastrosAuxiliares.dto.basico.GrauInstrucaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Grau de Instrução", description = "Exemplo da resposta contendo as informações de um grau de instrução")
public class ExemploRespostaGrauInstrucao {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private GrauInstrucaoDTO content;

    public GrauInstrucaoDTO getContent() {
        return content;
    }

    public void setContent(GrauInstrucaoDTO content) {
        this.content = content;
    }
}
