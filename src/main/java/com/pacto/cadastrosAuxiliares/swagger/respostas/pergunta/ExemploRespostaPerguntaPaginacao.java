package com.pacto.cadastrosAuxiliares.swagger.respostas.pergunta;

import com.pacto.cadastrosAuxiliares.dto.basico.PerguntaDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Perguntas Paginada", description = "Exemplo da resposta contendo uma lista paginada de perguntas para questionários de academias")
public class ExemploRespostaPerguntaPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de perguntas encontradas na consulta")
    private List<PerguntaDTO> content;

    public List<PerguntaDTO> getContent() {
        return content;
    }

    public void setContent(List<PerguntaDTO> content) {
        this.content = content;
    }
}
