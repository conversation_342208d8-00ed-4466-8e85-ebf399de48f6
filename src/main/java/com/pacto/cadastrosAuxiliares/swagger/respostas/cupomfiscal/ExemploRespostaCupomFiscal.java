package com.pacto.cadastrosAuxiliares.swagger.respostas.cupomfiscal;

import com.pacto.cadastrosAuxiliares.dto.basico.CupomFiscalDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Cupom Fiscal", description = "Exemplo da resposta contendo as informações de um cupom fiscal")
public class ExemploRespostaCupomFiscal {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private CupomFiscalDTO content;

    public CupomFiscalDTO getContent() {
        return content;
    }

    public void setContent(CupomFiscalDTO content) {
        this.content = content;
    }
}
