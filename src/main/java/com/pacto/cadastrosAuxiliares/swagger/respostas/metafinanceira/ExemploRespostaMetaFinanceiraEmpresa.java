package com.pacto.cadastrosAuxiliares.swagger.respostas.metafinanceira;

import com.pacto.cadastrosAuxiliares.dto.basico.MetaFinanceiraEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Meta Financeira Empresa", description = "Exemplo da resposta contendo as informações de uma meta financeira de academia")
public class ExemploRespostaMetaFinanceiraEmpresa {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private MetaFinanceiraEmpresaDTO content;

    public MetaFinanceiraEmpresaDTO getContent() {
        return content;
    }

    public void setContent(MetaFinanceiraEmpresaDTO content) {
        this.content = content;
    }
}
