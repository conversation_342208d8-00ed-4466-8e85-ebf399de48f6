package com.pacto.cadastrosAuxiliares.swagger.respostas.justificativaoperacao;

import com.pacto.cadastrosAuxiliares.dto.basico.JustificativaOperacaoDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta Lista Justificativa de Operação Paginação", description = "Exemplo de resposta para consulta paginada de justificativas de operação")
public class ExemploRespostaListJustificativaOperacaoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de justificativas de operação encontradas")
    private List<JustificativaOperacaoDTO> content;

    public List<JustificativaOperacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<JustificativaOperacaoDTO> content) {
        this.content = content;
    }
}
