package com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto;

import io.swagger.v3.oas.annotations.media.Schema;

public class ExemploRespostaQuantidadeProdutos {
    
    @Schema(description = "Quantidade de produtos de academia atualizados com as configurações de imposto", example = "25")
    private Integer content;

    public Integer getContent() {
        return content;
    }

    public void setContent(Integer content) {
        this.content = content;
    }
}
