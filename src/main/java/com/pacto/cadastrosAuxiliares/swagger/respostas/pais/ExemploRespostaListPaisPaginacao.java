package com.pacto.cadastrosAuxiliares.swagger.respostas.pais;

import com.pacto.cadastrosAuxiliares.dto.basico.PaisDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Países com Paginação", description = "Exemplo da resposta contendo uma lista paginada de países")
public class ExemploRespostaListPaisPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<PaisDTO> content;

    public List<PaisDTO> getContent() {
        return content;
    }

    public void setContent(List<PaisDTO> content) {
        this.content = content;
    }
}
