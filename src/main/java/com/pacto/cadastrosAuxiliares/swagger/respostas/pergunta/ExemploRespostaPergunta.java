package com.pacto.cadastrosAuxiliares.swagger.respostas.pergunta;

import com.pacto.cadastrosAuxiliares.dto.basico.PerguntaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Pergunta", description = "Exemplo da resposta contendo as informações de uma pergunta específica")
public class ExemploRespostaPergunta {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private PerguntaDTO content;

    public PerguntaDTO getContent() {
        return content;
    }

    public void setContent(PerguntaDTO content) {
        this.content = content;
    }
}
