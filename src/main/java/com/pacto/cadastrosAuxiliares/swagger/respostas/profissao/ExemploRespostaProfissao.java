package com.pacto.cadastrosAuxiliares.swagger.respostas.profissao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta Profissão", description = "Exemplo de resposta para consulta de uma profissão específica")
public class ExemploRespostaProfissao {
    
    @Schema(description = "Código identificador único da profissão", example = "1")
    private Integer codigo;
    
    @Schema(description = "Nome da profissão exercida na academia", example = "Educador Físico")
    private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
