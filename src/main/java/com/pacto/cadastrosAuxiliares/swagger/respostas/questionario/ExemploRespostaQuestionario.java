package com.pacto.cadastrosAuxiliares.swagger.respostas.questionario;

import com.pacto.cadastrosAuxiliares.dto.basico.QuestionarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Questionário", description = "Exemplo da resposta contendo as informações de um questionário específico")
public class ExemploRespostaQuestionario {

    @Schema(description = "Conte<PERSON><PERSON> da resposta contendo as informações solicitadas na requisição")
    private QuestionarioDTO content;

    public QuestionarioDTO getContent() {
        return content;
    }

    public void setContent(QuestionarioDTO content) {
        this.content = content;
    }
}
