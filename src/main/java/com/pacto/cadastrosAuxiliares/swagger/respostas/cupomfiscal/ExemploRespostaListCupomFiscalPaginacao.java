package com.pacto.cadastrosAuxiliares.swagger.respostas.cupomfiscal;

import com.pacto.cadastrosAuxiliares.dto.basico.CupomFiscalDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Cupons Fiscais com Paginação", description = "Exemplo da resposta contendo uma lista paginada de cupons fiscais")
public class ExemploRespostaListCupomFiscalPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<CupomFiscalDTO> content;

    public List<CupomFiscalDTO> getContent() {
        return content;
    }

    public void setContent(List<CupomFiscalDTO> content) {
        this.content = content;
    }
}
