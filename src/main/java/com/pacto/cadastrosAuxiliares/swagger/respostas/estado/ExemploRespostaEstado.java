package com.pacto.cadastrosAuxiliares.swagger.respostas.estado;

import com.pacto.cadastrosAuxiliares.dto.basico.EstadoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Estado", description = "Exemplo da resposta contendo as informações de um estado")
public class ExemploRespostaEstado {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private EstadoDTO content;

    public EstadoDTO getContent() {
        return content;
    }

    public void setContent(EstadoDTO content) {
        this.content = content;
    }
}
