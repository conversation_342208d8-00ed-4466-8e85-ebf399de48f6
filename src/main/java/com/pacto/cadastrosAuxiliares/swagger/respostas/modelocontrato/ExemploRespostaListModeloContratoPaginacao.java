package com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato;

import com.pacto.cadastrosAuxiliares.dto.basico.ModeloContratoDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Modelos de Contrato com Paginação", description = "Exemplo da resposta contendo uma lista paginada de modelos de contrato")
public class ExemploRespostaListModeloContratoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de modelos de contrato encontrados na requisição")
    private List<ModeloContratoDTO> content;

    public List<ModeloContratoDTO> getContent() {
        return content;
    }

    public void setContent(List<ModeloContratoDTO> content) {
        this.content = content;
    }
}
