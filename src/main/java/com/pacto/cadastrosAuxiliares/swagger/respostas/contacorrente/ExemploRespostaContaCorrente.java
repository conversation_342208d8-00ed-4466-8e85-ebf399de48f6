package com.pacto.cadastrosAuxiliares.swagger.respostas.contacorrente;

import com.pacto.cadastrosAuxiliares.dto.basico.ContaCorrenteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Conta Corrente", description = "Exemplo da resposta contendo as informações de uma conta corrente")
public class ExemploRespostaContaCorrente {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private ContaCorrenteDTO content;

    public ContaCorrenteDTO getContent() {
        return content;
    }

    public void setContent(ContaCorrenteDTO content) {
        this.content = content;
    }
}
