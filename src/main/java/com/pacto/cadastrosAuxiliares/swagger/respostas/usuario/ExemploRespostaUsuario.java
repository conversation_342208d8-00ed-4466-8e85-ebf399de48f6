package com.pacto.cadastrosAuxiliares.swagger.respostas.usuario;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Usuário", description = "Exemplo da resposta contendo as informações de um usuário do sistema")
public class ExemploRespostaUsuario {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private UsuarioSimplesDTOExemplo content;

    public UsuarioSimplesDTOExemplo getContent() {
        return content;
    }

    public void setContent(UsuarioSimplesDTOExemplo content) {
        this.content = content;
    }
}
