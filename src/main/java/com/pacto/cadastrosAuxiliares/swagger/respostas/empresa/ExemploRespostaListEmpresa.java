package com.pacto.cadastrosAuxiliares.swagger.respostas.empresa;

import com.pacto.cadastrosAuxiliares.dto.basico.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Empresas", description = "Exemplo da resposta contendo uma lista de empresas")
public class ExemploRespostaListEmpresa {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private List<EmpresaDTO> content;

    public List<EmpresaDTO> getContent() {
        return content;
    }

    public void setContent(List<EmpresaDTO> content) {
        this.content = content;
    }
}
