package com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao;

import com.pacto.cadastrosAuxiliares.dto.basico.GrauInstrucaoDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Graus de Instrução Paginada", description = "Exemplo da resposta contendo uma lista paginada de graus de instrução")
public class ExemploRespostaListGrauInstrucaoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de graus de instrução encontrados na consulta")
    private List<GrauInstrucaoDTO> content;

    public List<GrauInstrucaoDTO> getContent() {
        return content;
    }

    public void setContent(List<GrauInstrucaoDTO> content) {
        this.content = content;
    }
}
