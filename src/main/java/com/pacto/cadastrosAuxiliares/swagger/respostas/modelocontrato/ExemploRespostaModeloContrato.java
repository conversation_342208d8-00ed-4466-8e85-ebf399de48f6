package com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato;

import com.pacto.cadastrosAuxiliares.dto.basico.ModeloContratoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Modelo de Contrato", description = "Exemplo da resposta contendo as informações de um modelo de contrato")
public class ExemploRespostaModeloContrato {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private ModeloContratoDTO content;

    public ModeloContratoDTO getContent() {
        return content;
    }

    public void setContent(ModeloContratoDTO content) {
        this.content = content;
    }
}
