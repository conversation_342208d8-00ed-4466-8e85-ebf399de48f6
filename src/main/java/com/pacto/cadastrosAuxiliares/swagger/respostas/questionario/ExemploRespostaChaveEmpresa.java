package com.pacto.cadastrosAuxiliares.swagger.respostas.questionario;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Chave da Empresa", description = "Exemplo da resposta contendo a chave gerada para upload de imagens da empresa")
public class ExemploRespostaChaveEmpresa {

    @Schema(description = "Chave única gerada para identificar uploads de imagens da empresa no sistema de mídia", example = "EMP_123_USR_456_2024_IMG_KEY")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
