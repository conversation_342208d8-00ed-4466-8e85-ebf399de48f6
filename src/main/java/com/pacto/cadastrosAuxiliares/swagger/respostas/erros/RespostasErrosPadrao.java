package com.pacto.cadastrosAuxiliares.swagger.respostas.erros;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

/**
 * Classe utilitária que centraliza as respostas de erro padrão para reutilização
 * em todos os endpoints do microsserviço de cadastros auxiliares.
 */
public class RespostasErrosPadrao {

    /**
     * Resposta padrão para erro 400 - Bad Request
     * Utilizada quando há problemas de validação ou dados inválidos na requisição
     */
    public static final ApiResponse BAD_REQUEST = new ApiResponse() {
        @Override
        public String responseCode() {
            return "400";
        }

        @Override
        public String description() {
            return "Requisição inválida - Dados fornecidos são inválidos ou estão em formato incorreto";
        }

        @Override
        public Content[] content() {
            return new Content[]{
                new Content() {
                    @Override
                    public String mediaType() {
                        return "application/json";
                    }

                    @Override
                    public Schema schema() {
                        return new Schema() {
                            @Override
                            public Class<?> implementation() {
                                return ExemploRespostaErroBadRequest.class;
                            }

                            @Override
                            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                                return Schema.class;
                            }
                        };
                    }

                    @Override
                    public Class<? extends java.lang.annotation.Annotation> annotationType() {
                        return Content.class;
                    }
                }
            };
        }

        @Override
        public Class<? extends java.lang.annotation.Annotation> annotationType() {
            return ApiResponse.class;
        }
    };

    /**
     * Resposta padrão para erro 401 - Unauthorized
     * Utilizada quando há problemas de autenticação
     */
    public static final ApiResponse UNAUTHORIZED = new ApiResponse() {
        @Override
        public String responseCode() {
            return "401";
        }

        @Override
        public String description() {
            return "Não autorizado - Token de acesso inválido ou expirado";
        }

        @Override
        public Content[] content() {
            return new Content[]{
                new Content() {
                    @Override
                    public String mediaType() {
                        return "application/json";
                    }

                    @Override
                    public Schema schema() {
                        return new Schema() {
                            @Override
                            public Class<?> implementation() {
                                return ExemploRespostaErroUnauthorized.class;
                            }

                            @Override
                            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                                return Schema.class;
                            }
                        };
                    }

                    @Override
                    public Class<? extends java.lang.annotation.Annotation> annotationType() {
                        return Content.class;
                    }
                }
            };
        }

        @Override
        public Class<? extends java.lang.annotation.Annotation> annotationType() {
            return ApiResponse.class;
        }
    };

    /**
     * Resposta padrão para erro 403 - Forbidden
     * Utilizada quando o usuário não possui permissão para acessar o recurso
     */
    public static final ApiResponse FORBIDDEN = new ApiResponse() {
        @Override
        public String responseCode() {
            return "403";
        }

        @Override
        public String description() {
            return "Acesso negado - Permissões insuficientes para realizar esta operação";
        }

        @Override
        public Content[] content() {
            return new Content[]{
                new Content() {
                    @Override
                    public String mediaType() {
                        return "application/json";
                    }

                    @Override
                    public Schema schema() {
                        return new Schema() {
                            @Override
                            public Class<?> implementation() {
                                return ExemploRespostaErroForbidden.class;
                            }

                            @Override
                            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                                return Schema.class;
                            }
                        };
                    }

                    @Override
                    public Class<? extends java.lang.annotation.Annotation> annotationType() {
                        return Content.class;
                    }
                }
            };
        }

        @Override
        public Class<? extends java.lang.annotation.Annotation> annotationType() {
            return ApiResponse.class;
        }
    };

    /**
     * Resposta padrão para erro 404 - Not Found
     * Utilizada quando um recurso solicitado não é encontrado
     */
    public static final ApiResponse NOT_FOUND = new ApiResponse() {
        @Override
        public String responseCode() {
            return "404";
        }

        @Override
        public String description() {
            return "Recurso não encontrado - O recurso solicitado não existe ou não foi encontrado";
        }

        @Override
        public Content[] content() {
            return new Content[]{
                new Content() {
                    @Override
                    public String mediaType() {
                        return "application/json";
                    }

                    @Override
                    public Schema schema() {
                        return new Schema() {
                            @Override
                            public Class<?> implementation() {
                                return ExemploRespostaErroNotFound.class;
                            }

                            @Override
                            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                                return Schema.class;
                            }
                        };
                    }

                    @Override
                    public Class<? extends java.lang.annotation.Annotation> annotationType() {
                        return Content.class;
                    }
                }
            };
        }

        @Override
        public Class<? extends java.lang.annotation.Annotation> annotationType() {
            return ApiResponse.class;
        }
    };

    /**
     * Resposta padrão para erro 500 - Internal Server Error
     * Utilizada quando ocorrem erros internos do servidor
     */
    public static final ApiResponse INTERNAL_SERVER_ERROR = new ApiResponse() {
        @Override
        public String responseCode() {
            return "500";
        }

        @Override
        public String description() {
            return "Erro interno do servidor - Ocorreu um erro inesperado no processamento da requisição";
        }

        @Override
        public Content[] content() {
            return new Content[]{
                new Content() {
                    @Override
                    public String mediaType() {
                        return "application/json";
                    }

                    @Override
                    public Schema schema() {
                        return new Schema() {
                            @Override
                            public Class<?> implementation() {
                                return ExemploRespostaErroInterno.class;
                            }

                            @Override
                            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                                return Schema.class;
                            }
                        };
                    }

                    @Override
                    public Class<? extends java.lang.annotation.Annotation> annotationType() {
                        return Content.class;
                    }
                }
            };
        }

        @Override
        public Class<? extends java.lang.annotation.Annotation> annotationType() {
            return ApiResponse.class;
        }
    };
}
