package com.pacto.cadastrosAuxiliares.swagger.respostas.indicefinanceiro;

import com.pacto.cadastrosAuxiliares.dto.basico.IndiceFinanceiroDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Índice Financeiro", description = "Exemplo da resposta contendo as informações de um índice financeiro")
public class ExemploRespostaIndiceFinanceiro {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private IndiceFinanceiroDTO content;

    public IndiceFinanceiroDTO getContent() {
        return content;
    }

    public void setContent(IndiceFinanceiroDTO content) {
        this.content = content;
    }
}
