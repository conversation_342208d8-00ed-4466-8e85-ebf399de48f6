package com.pacto.cadastrosAuxiliares.swagger.respostas.servidorfacial;

import com.pacto.cadastrosAuxiliares.dto.basico.ServidorFacialDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Servidores Faciais com Paginação", description = "Exemplo da resposta contendo uma lista paginada de servidores de reconhecimento facial")
public class ExemploRespostaListServidorFacialPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<ServidorFacialDTO> content;

    public List<ServidorFacialDTO> getContent() {
        return content;
    }

    public void setContent(List<ServidorFacialDTO> content) {
        this.content = content;
    }
}
