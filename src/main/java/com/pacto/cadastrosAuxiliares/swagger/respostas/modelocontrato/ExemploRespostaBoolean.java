package com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Boolean", description = "Exemplo da resposta contendo um valor booleano")
public class ExemploRespostaBoolean {

    @Schema(description = "Valor booleano da resposta", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
