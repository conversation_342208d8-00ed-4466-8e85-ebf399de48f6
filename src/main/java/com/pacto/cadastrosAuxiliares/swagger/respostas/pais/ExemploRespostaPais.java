package com.pacto.cadastrosAuxiliares.swagger.respostas.pais;

import com.pacto.cadastrosAuxiliares.dto.basico.PaisDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - País", description = "Exemplo da resposta contendo as informações de um país")
public class ExemploRespostaPais {

    @Schema(description = "Dados do país solicitado")
    private PaisDTO content;

    public PaisDTO getContent() {
        return content;
    }

    public void setContent(PaisDTO content) {
        this.content = content;
    }
}
