package com.pacto.cadastrosAuxiliares.swagger.respostas.operadoracartao;

import com.pacto.cadastrosAuxiliares.dto.basico.OperadoraCartaoDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista Operadora de Cartão Paginação", description = "Exemplo da resposta contendo uma lista paginada de operadoras de cartão")
public class ExemploRespostaListOperadoraCartaoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<OperadoraCartaoDTO> content;

    public List<OperadoraCartaoDTO> getContent() {
        return content;
    }

    public void setContent(List<OperadoraCartaoDTO> content) {
        this.content = content;
    }
}
