package com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato;

import com.pacto.cadastrosAuxiliares.entities.basico.ModeloContratoRedeEmpresa;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class ExemploRespostaListModeloContratoRedeEmpresaPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de modelos de contrato rede empresa encontrados")
    private List<ModeloContratoRedeEmpresa> content;

    public List<ModeloContratoRedeEmpresa> getContent() {
        return content;
    }

    public void setContent(List<ModeloContratoRedeEmpresa> content) {
        this.content = content;
    }
}
