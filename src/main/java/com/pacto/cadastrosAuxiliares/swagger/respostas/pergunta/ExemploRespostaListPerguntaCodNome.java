package com.pacto.cadastrosAuxiliares.swagger.respostas.pergunta;

import com.pacto.cadastrosAuxiliares.dto.basico.PerguntaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Perguntas Código e Nome", description = "Exemplo da resposta contendo uma lista simplificada de perguntas com código e descrição")
public class ExemploRespostaListPerguntaCodNome {

    @Schema(description = "Lista de perguntas com informações básicas (código e descrição)")
    private List<PerguntaDTO> content;

    public List<PerguntaDTO> getContent() {
        return content;
    }

    public void setContent(List<PerguntaDTO> content) {
        this.content = content;
    }
}
