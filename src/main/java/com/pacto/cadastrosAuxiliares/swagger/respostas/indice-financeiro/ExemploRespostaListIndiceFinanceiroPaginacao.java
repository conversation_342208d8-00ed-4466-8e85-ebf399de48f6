package com.pacto.cadastrosAuxiliares.swagger.respostas.indicefinanceiro;

import com.pacto.cadastrosAuxiliares.dto.basico.IndiceFinanceiroDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Índices Financeiros Paginada", description = "Exemplo da resposta contendo uma lista paginada de índices financeiros")
public class ExemploRespostaListIndiceFinanceiroPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<IndiceFinanceiroDTO> content;

    public List<IndiceFinanceiroDTO> getContent() {
        return content;
    }

    public void setContent(List<IndiceFinanceiroDTO> content) {
        this.content = content;
    }
}
