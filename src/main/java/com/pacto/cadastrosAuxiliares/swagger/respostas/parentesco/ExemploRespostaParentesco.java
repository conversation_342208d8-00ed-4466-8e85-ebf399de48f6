package com.pacto.cadastrosAuxiliares.swagger.respostas.parentesco;

import com.pacto.cadastrosAuxiliares.dto.basico.ParentescoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Parentesco", description = "Exemplo da resposta contendo as informações de um parentesco")
public class ExemploRespostaParentesco {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private ParentescoDTO content;

    public ParentescoDTO getContent() {
        return content;
    }

    public void setContent(ParentescoDTO content) {
        this.content = content;
    }
}
