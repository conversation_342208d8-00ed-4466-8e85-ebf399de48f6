package com.pacto.cadastrosAuxiliares.swagger.respostas.usuario;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Usuário Simples - Exemplo", description = "Exemplo das informações básicas de um usuário do sistema de academias")
public class UsuarioSimplesDTOExemplo {

    @Schema(description = "Código identificador único do usuário no sistema", example = "15")
    private Integer codZw;

    @Schema(description = "Código identificador do treino associado ao usuário", example = "7")
    private Integer codTreino;

    @Schema(description = "Código do perfil de treino do usuário para personalização de exercícios", example = "2")
    private Integer perfilTreino;

    @Schema(description = "Código do perfil do usuário no sistema ZW", example = "12")
    private Integer perfilZw;

    @Schema(description = "Chave de acesso do usuário para autenticação", example = "abc123def456")
    private String chave;

    @Schema(description = "Nome de usuário para login no sistema da academia", example = "carlos.instrutor")
    private String username;

    @Schema(description = "Código identificador da empresa/academia associada ao usuário", example = "3")
    private Integer idEmpresa;

    @Schema(description = "Token de autenticação JWT do usuário", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "Indica se o usuário possui privilégios de administrador do sistema", example = "false")
    private Boolean administrador;

    public UsuarioSimplesDTOExemplo() {
    }

    public Integer getCodZw() {
        return codZw;
    }

    public void setCodZw(Integer codZw) {
        this.codZw = codZw;
    }

    public Integer getCodTreino() {
        return codTreino;
    }

    public void setCodTreino(Integer codTreino) {
        this.codTreino = codTreino;
    }

    public Integer getPerfilTreino() {
        return perfilTreino;
    }

    public void setPerfilTreino(Integer perfilTreino) {
        this.perfilTreino = perfilTreino;
    }

    public Integer getPerfilZw() {
        return perfilZw;
    }

    public void setPerfilZw(Integer perfilZw) {
        this.perfilZw = perfilZw;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(Integer idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Boolean getAdministrador() {
        return administrador;
    }

    public void setAdministrador(Boolean administrador) {
        this.administrador = administrador;
    }
}
