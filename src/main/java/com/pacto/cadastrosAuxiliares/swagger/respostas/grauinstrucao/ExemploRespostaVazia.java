package com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Operação Realizada", description = "Exemplo da resposta para operações que não retornam conteúdo específico")
public class ExemploRespostaVazia {

    @Schema(description = "Indica que a operação foi realizada com sucesso", example = "true")
    private Boolean success;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
