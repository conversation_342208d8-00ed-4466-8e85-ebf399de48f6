package com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto;

import com.pacto.cadastrosAuxiliares.dto.LogDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;

import java.util.List;

public class ExemploRespostaListLogsPaginacao extends ExemploPaginadorResposta {
    
    private List<LogDTO> content;

    public List<LogDTO> getContent() {
        return content;
    }

    public void setContent(List<LogDTO> content) {
        this.content = content;
    }
}
