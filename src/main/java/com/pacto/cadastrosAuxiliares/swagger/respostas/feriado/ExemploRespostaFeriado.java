package com.pacto.cadastrosAuxiliares.swagger.respostas.feriado;

import com.pacto.cadastrosAuxiliares.dto.basico.FeriadoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Feriado", description = "Exemplo da resposta contendo as informações de um feriado")
public class ExemploRespostaFeriado {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private FeriadoDTO content;

    public FeriadoDTO getContent() {
        return content;
    }

    public void setContent(FeriadoDTO content) {
        this.content = content;
    }
}
