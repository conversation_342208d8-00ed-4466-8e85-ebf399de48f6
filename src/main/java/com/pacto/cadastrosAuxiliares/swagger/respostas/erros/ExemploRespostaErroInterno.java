package com.pacto.cadastrosAuxiliares.swagger.respostas.erros;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Erro Interno", description = "Exemplo da resposta para erros internos do servidor")
public class ExemploRespostaErroInterno {

    @Schema(description = "Chave identificadora do erro para localização e tratamento específico", example = "error.internal.database")
    private String chaveExcecao;

    @Schema(description = "Mensagem descritiva do erro interno do sistema", example = "Erro interno do servidor. Contate o administrador do sistema.")
    private String mensagem;

    @Schema(description = "Código de status HTTP da resposta", example = "500")
    private Integer status;

    @Schema(description = "Timestamp do momento em que o erro ocorreu", example = "2024-07-03T10:30:00.000Z")
    private String timestamp;

    @Schema(description = "Detalhes técnicos do erro para depuração (apenas em ambiente de desenvolvimento)", example = "java.sql.SQLException: Connection timeout")
    private String detalhes;

    public String getChaveExcecao() {
        return chaveExcecao;
    }

    public void setChaveExcecao(String chaveExcecao) {
        this.chaveExcecao = chaveExcecao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getDetalhes() {
        return detalhes;
    }

    public void setDetalhes(String detalhes) {
        this.detalhes = detalhes;
    }
}
