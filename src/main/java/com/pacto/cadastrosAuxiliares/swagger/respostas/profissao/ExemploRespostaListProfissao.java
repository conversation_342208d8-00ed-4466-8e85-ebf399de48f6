package com.pacto.cadastrosAuxiliares.swagger.respostas.profissao;

import com.pacto.cadastrosAuxiliares.dto.basico.ProfissaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta Lista Profissão", description = "Exemplo de resposta para consulta de todas as profissões sem paginação")
public class ExemploRespostaListProfissao {
    
    @Schema(description = "Lista completa de profissões disponíveis")
    private List<ProfissaoDTO> content;

    public List<ProfissaoDTO> getContent() {
        return content;
    }

    public void setContent(List<ProfissaoDTO> content) {
        this.content = content;
    }
}
