package com.pacto.cadastrosAuxiliares.swagger.respostas.questionario;

import com.pacto.cadastrosAuxiliares.dto.basico.QuestionarioDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Questionários Paginada", description = "Exemplo da resposta contendo uma lista paginada de questionários da academia")
public class ExemploRespostaQuestionarioPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de questionários encontrados na consulta")
    private List<QuestionarioDTO> content;

    public List<QuestionarioDTO> getContent() {
        return content;
    }

    public void setContent(List<QuestionarioDTO> content) {
        this.content = content;
    }
}
