package com.pacto.cadastrosAuxiliares.swagger.respostas.estado;

import com.pacto.cadastrosAuxiliares.dto.basico.EstadoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Estados", description = "Exemplo da resposta contendo uma lista de estados")
public class ExemploRespostaListEstado {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<EstadoDTO> content;

    public List<EstadoDTO> getContent() {
        return content;
    }

    public void setContent(List<EstadoDTO> content) {
        this.content = content;
    }
}
