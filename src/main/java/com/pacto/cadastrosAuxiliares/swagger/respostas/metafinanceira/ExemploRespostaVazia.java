package com.pacto.cadastrosAuxiliares.swagger.respostas.metafinanceira;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Vazia", description = "Exemplo da resposta para operações que não retornam conteúdo específico")
public class ExemploRespostaVazia {

    @Schema(description = "Conteúdo vazio da resposta", example = "null")
    private Object content;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }
}
