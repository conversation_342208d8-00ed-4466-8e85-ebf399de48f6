package com.pacto.cadastrosAuxiliares.swagger.respostas.erros;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Erro de Acesso Negado", description = "Exemplo da resposta para erros de acesso negado ou permissões insuficientes")
public class ExemploRespostaErroForbidden {

    @Schema(description = "Chave identificadora do erro para localização e tratamento específico", example = "error.forbidden.permission")
    private String chaveExcecao;

    @Schema(description = "Mensagem descritiva do erro de acesso negado", example = "Acesso negado. Você não possui permissão para realizar esta operação.")
    private String mensagem;

    @Schema(description = "Código de status HTTP da resposta", example = "403")
    private Integer status;

    @Schema(description = "Timestamp do momento em que o erro ocorreu", example = "2024-07-03T10:30:00.000Z")
    private String timestamp;

    public String getChaveExcecao() {
        return chaveExcecao;
    }

    public void setChaveExcecao(String chaveExcecao) {
        this.chaveExcecao = chaveExcecao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
}
