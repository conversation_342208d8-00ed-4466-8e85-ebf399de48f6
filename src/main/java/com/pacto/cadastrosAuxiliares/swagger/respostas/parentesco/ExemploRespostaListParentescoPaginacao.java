package com.pacto.cadastrosAuxiliares.swagger.respostas.parentesco;

import com.pacto.cadastrosAuxiliares.dto.basico.ParentescoDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista Parentesco Paginação", description = "Exemplo da resposta contendo uma lista paginada de parentescos")
public class ExemploRespostaListParentescoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<ParentescoDTO> content;

    public List<ParentescoDTO> getContent() {
        return content;
    }

    public void setContent(List<ParentescoDTO> content) {
        this.content = content;
    }
}
