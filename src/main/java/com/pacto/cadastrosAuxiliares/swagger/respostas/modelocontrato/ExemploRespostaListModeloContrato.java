package com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato;

import com.pacto.cadastrosAuxiliares.dto.basico.ModeloContratoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Modelos de Contrato", description = "Exemplo da resposta contendo uma lista de modelos de contrato")
public class ExemploRespostaListModeloContrato {

    @Schema(description = "Lista de modelos de contrato encontrados na requisição")
    private List<ModeloContratoDTO> content;

    public List<ModeloContratoDTO> getContent() {
        return content;
    }

    public void setContent(List<ModeloContratoDTO> content) {
        this.content = content;
    }
}
