package com.pacto.cadastrosAuxiliares.swagger.respostas.questionariopergunta;

import com.pacto.cadastrosAuxiliares.dto.basico.QuestionarioperguntaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Associação Questionário-Pergunta", description = "Exemplo da resposta contendo as informações de uma associação específica entre questionário e pergunta")
public class ExemploRespostaQuestionarioPergunta {

    @Schema(description = "Conteúdo da resposta contendo as informações da associação questionário-pergunta solicitada")
    private QuestionarioperguntaDTO content;

    public QuestionarioperguntaDTO getContent() {
        return content;
    }

    public void setContent(QuestionarioperguntaDTO content) {
        this.content = content;
    }
}
