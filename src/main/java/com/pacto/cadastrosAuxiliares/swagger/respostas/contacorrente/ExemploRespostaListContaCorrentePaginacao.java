package com.pacto.cadastrosAuxiliares.swagger.respostas.contacorrente;

import com.pacto.cadastrosAuxiliares.dto.basico.ContaCorrenteDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Contas Correntes com Paginação", description = "Exemplo da resposta contendo uma lista paginada de contas correntes")
public class ExemploRespostaListContaCorrentePaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private List<ContaCorrenteDTO> content;

    public List<ContaCorrenteDTO> getContent() {
        return content;
    }

    public void setContent(List<ContaCorrenteDTO> content) {
        this.content = content;
    }
}
