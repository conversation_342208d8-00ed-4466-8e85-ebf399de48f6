package com.pacto.cadastrosAuxiliares.swagger.respostas.tamanhoarmario;

import com.pacto.cadastrosAuxiliares.dto.basico.TamanhoArmarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Tamanho de Armário", description = "Exemplo da resposta contendo as informações de um tamanho de armário")
public class ExemploRespostaTamanhoArmario {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private TamanhoArmarioDTO content;

    public TamanhoArmarioDTO getContent() {
        return content;
    }

    public void setContent(TamanhoArmarioDTO content) {
        this.content = content;
    }
}
