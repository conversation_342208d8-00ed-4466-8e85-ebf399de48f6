package com.pacto.cadastrosAuxiliares.swagger.respostas.operadoracartao;

import com.pacto.cadastrosAuxiliares.dto.basico.OperadoraCartaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Operadora de Cartão", description = "Exemplo da resposta contendo as informações de uma operadora de cartão")
public class ExemploRespostaOperadoraCartao {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private OperadoraCartaoDTO content;

    public OperadoraCartaoDTO getContent() {
        return content;
    }

    public void setContent(OperadoraCartaoDTO content) {
        this.content = content;
    }
}
