package com.pacto.cadastrosAuxiliares.swagger.respostas.questionario;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - URL da Imagem", description = "Exemplo da resposta contendo a URL da imagem do questionário")
public class ExemploRespostaUrlImagem {

    @Schema(description = "URL completa da imagem do questionário hospedada no sistema de mídia", example = "https://media.academia.com/images/questionarios/background-pesquisa-satisfacao.jpg")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
