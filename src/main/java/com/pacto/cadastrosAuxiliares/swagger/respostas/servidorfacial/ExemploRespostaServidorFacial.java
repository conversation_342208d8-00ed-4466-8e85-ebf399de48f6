package com.pacto.cadastrosAuxiliares.swagger.respostas.servidorfacial;

import com.pacto.cadastrosAuxiliares.dto.basico.ServidorFacialDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Servidor Facial", description = "Exemplo da resposta contendo as informações de um servidor de reconhecimento facial")
public class ExemploRespostaServidorFacial {

    @Schema(description = "Conteúdo da resposta contendo as informações solicitadas na requisição")
    private ServidorFacialDTO content;

    public ServidorFacialDTO getContent() {
        return content;
    }

    public void setContent(ServidorFacialDTO content) {
        this.content = content;
    }
}
