package com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto;

import com.pacto.cadastrosAuxiliares.dto.basico.ImpostoProdutoCfopDTO;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorResposta;

import java.util.List;

public class ExemploRespostaListImpostoProdutoPaginacao extends ExemploPaginadorResposta {
    
    private List<ImpostoProdutoCfopDTO> content;

    public List<ImpostoProdutoCfopDTO> getContent() {
        return content;
    }

    public void setContent(List<ImpostoProdutoCfopDTO> content) {
        this.content = content;
    }
}
