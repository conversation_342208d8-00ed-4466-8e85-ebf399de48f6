package com.pacto.cadastrosAuxiliares.swagger;


import com.pacto.cadastrosAuxiliares.swagger.respostas.erros.*;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.tags.Tag;

import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

import java.util.Arrays;
import java.util.List;



@Configuration
public class OpenApiConfiguration {
    
    private static final String SECURITY_SCHEME_NAME = "bearerAuth";
    
    @Value("${swagger.config.info.title}")
    private String title;

    @Value("${swagger.config.info.description}")
    private String description;

    @Value("${swagger.config.info.version}")
    private String version;
    
    @Value("${swagger.config.info.license-name}")
    private String licenseName;
    
    @Value("${swagger.config.info.license-url}")
    private String licenseUrl;
    
    @Bean
    public OpenAPI customOpenAPI() {
        Info apiInfo = new Info()
                .title(title)
                .description(description)
                .version(version)
                .license(new License().name(licenseName).url(licenseUrl));
        SecurityScheme bearerScheme = new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .in(SecurityScheme.In.HEADER)
                .name("Authorization");
        // Criar componentes com schemas de erro e segurança
        Components components = new Components()
                .addSecuritySchemes(SECURITY_SCHEME_NAME, bearerScheme);

        return new OpenAPI()
                .info(apiInfo)
                .tags(createTags())
                .components(components)
                .addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME));
    }

    private List<Tag> createTags() {
        return Arrays.asList(
                new Tag().name(SwaggerTags.ADQUIRENTE).description(SwaggerTags.ADQUIRENTE_DESCRICAO),
                new Tag().name(SwaggerTags.BANCO).description(SwaggerTags.BANCO_DESCRICAO),
                new Tag().name(SwaggerTags.CATEGORIA).description(SwaggerTags.CATEGORIA_DESCRICAO),
                new Tag().name(SwaggerTags.CIDADE).description(SwaggerTags.CIDADE_DESCRICAO),
                new Tag().name(SwaggerTags.CLASSIFICACAO).description(SwaggerTags.CLASSIFICACAO_DESCRICAO),
                new Tag().name(SwaggerTags.COLABORADORES).description(SwaggerTags.COLABORADORES_DESCRICAO),
                new Tag().name(SwaggerTags.CONFIGURACOES_SISTEMA).description(SwaggerTags.CONFIGURACOES_SISTEMA_DESCRICAO),
                new Tag().name(SwaggerTags.CONTA_CORRENTE).description(SwaggerTags.CONTA_CORRENTE_DESCRICAO),
                new Tag().name(SwaggerTags.DEPARTAMENTO).description(SwaggerTags.DEPARTAMENTO_DESCRICAO),
                new Tag().name(SwaggerTags.EMPRESA).description(SwaggerTags.EMPRESA_DESCRICAO),
                new Tag().name(SwaggerTags.ESTADO).description(SwaggerTags.ESTADO_DESCRICAO),
                new Tag().name(SwaggerTags.FERIADO).description(SwaggerTags.FERIADO_DESCRICAO),
                new Tag().name(SwaggerTags.GRAU_INSTRUCAO).description(SwaggerTags.GRAU_INSTRUCAO_DESCRICAO),
                new Tag().name(SwaggerTags.GRUPO).description(SwaggerTags.GRUPO_DESCRICAO),
                new Tag().name(SwaggerTags.HEALTH).description(SwaggerTags.HEALTH_DESCRICAO),
                new Tag().name(SwaggerTags.IMPOSTO).description(SwaggerTags.IMPOSTO_DESCRICAO),
                new Tag().name(SwaggerTags.INDICE_FINANCEIRO).description(SwaggerTags.INDICE_FINANCEIRO_DESCRICAO),
                new Tag().name(SwaggerTags.JUSTIFICATIVA_OPERACAO).description(SwaggerTags.JUSTIFICATIVA_OPERACAO_DESCRICAO),
                new Tag().name(SwaggerTags.LOCAL_DE_ACESSO).description(SwaggerTags.LOCAL_DE_ACESSO_DESCRICAO),
                new Tag().name(SwaggerTags.MALA_DIRETA).description(SwaggerTags.MALA_DIRETA_DESCRICAO),
                new Tag().name(SwaggerTags.META_FINANCEIRA).description(SwaggerTags.META_FINANCEIRA_DESCRICAO),
                new Tag().name(SwaggerTags.MODELO_CONTRATO).description(SwaggerTags.MODELO_CONTRATO_DESCRICAO),
                new Tag().name(SwaggerTags.NOTA_FISCAL).description(SwaggerTags.NOTA_FISCAL_DESCRICAO),
                new Tag().name(SwaggerTags.OPERADORA_CARTAO).description(SwaggerTags.OPERADORA_CARTAO_DESCRICAO),
                new Tag().name(SwaggerTags.PAIS).description(SwaggerTags.PAIS_DESCRICAO),
                new Tag().name(SwaggerTags.PARENTESCO).description(SwaggerTags.PARENTESCO_DESCRICAO),
                new Tag().name(SwaggerTags.PERGUNTA).description(SwaggerTags.PERGUNTA_DESCRICAO),
                new Tag().name(SwaggerTags.PESQUISA_SATISFACAO).description(SwaggerTags.PESQUISA_SATISFACAO_DESCRICAO),
                new Tag().name(SwaggerTags.PROFISSAO).description(SwaggerTags.PROFISSAO_DESCRICAO),
                new Tag().name(SwaggerTags.QUESTIONARIO).description(SwaggerTags.QUESTIONARIO_DESCRICAO),
                new Tag().name(SwaggerTags.SERVIDOR_FACIAL).description(SwaggerTags.SERVIDOR_FACIAL_DESCRICAO),
                new Tag().name(SwaggerTags.TAMANHO_ARMARIO).description(SwaggerTags.TAMANHO_ARMARIO_DESCRICAO),
                new Tag().name(SwaggerTags.USUARIO).description(SwaggerTags.USUARIO_DESCRICAO)
        );
    }

    @Bean
    public OpenApiCustomiser removeDeprecatedOperations() {
        return openApi -> {
            if (openApi.getPaths() == null) return;
            openApi.getPaths()
                    .entrySet()
                    .removeIf(entry ->
                            entry.getValue()
                                    .readOperations()
                                    .stream()
                                    .anyMatch(ioOperation -> Boolean.TRUE.equals(ioOperation.getDeprecated()))
                    );
        };
    }

    @Bean
    public OpenApiCustomiser addGlobalErrorResponses() {
        return openApi -> {
            if (openApi.getPaths() == null) return;

            // Criar respostas de erro padrão
            ApiResponse badRequestResponse = new ApiResponse()
                    .description("Requisição inválida - Dados fornecidos são inválidos ou estão em formato incorreto")
                    .content(new Content()
                            .addMediaType("application/json",
                                new MediaType().schema(new Schema<>().$ref("#/components/schemas/ExemploRespostaErroBadRequest"))));

            ApiResponse unauthorizedResponse = new ApiResponse()
                    .description("Não autorizado - Token de acesso inválido ou expirado")
                    .content(new Content()
                            .addMediaType("application/json",
                                new MediaType().schema(new Schema<>().$ref("#/components/schemas/ExemploRespostaErroUnauthorized"))));

            ApiResponse forbiddenResponse = new ApiResponse()
                    .description("Acesso negado - Permissões insuficientes para realizar esta operação")
                    .content(new Content()
                            .addMediaType("application/json",
                                new MediaType().schema(new Schema<>().$ref("#/components/schemas/ExemploRespostaErroForbidden"))));

            ApiResponse notFoundResponse = new ApiResponse()
                    .description("Recurso não encontrado - O recurso solicitado não existe ou não foi encontrado")
                    .content(new Content()
                            .addMediaType("application/json",
                                new MediaType().schema(new Schema<>().$ref("#/components/schemas/ExemploRespostaErroNotFound"))));

            ApiResponse internalServerErrorResponse = new ApiResponse()
                    .description("Erro interno do servidor - Ocorreu um erro inesperado no processamento da requisição")
                    .content(new Content()
                            .addMediaType("application/json",
                                new MediaType().schema(new Schema<>().$ref("#/components/schemas/ExemploRespostaErroInterno"))));

            // Adicionar as respostas de erro a todos os endpoints
            openApi.getPaths().values().forEach(pathItem -> {
                pathItem.readOperations().forEach(operation -> {
                    if (operation.getResponses() == null) {
                        operation.setResponses(new ApiResponses());
                    }

                    // Adicionar respostas de erro apenas se não existirem
                    if (!operation.getResponses().containsKey("400")) {
                        operation.getResponses().addApiResponse("400", badRequestResponse);
                    }
                    if (!operation.getResponses().containsKey("401")) {
                        operation.getResponses().addApiResponse("401", unauthorizedResponse);
                    }
                    if (!operation.getResponses().containsKey("403")) {
                        operation.getResponses().addApiResponse("403", forbiddenResponse);
                    }
                    if (!operation.getResponses().containsKey("404")) {
                        operation.getResponses().addApiResponse("404", notFoundResponse);
                    }
                    if (!operation.getResponses().containsKey("500")) {
                        operation.getResponses().addApiResponse("500", internalServerErrorResponse);
                    }
                });
            });
        };
    }
}