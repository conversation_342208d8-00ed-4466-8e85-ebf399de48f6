package com.pacto.cadastrosAuxiliares.swagger;

public class SwaggerTags {
    public static final String ADQUIRENTE = "Adquirente";
    public static final String ADQUIRENTE_DESCRICAO = "Operações de gestão de adquirentes";

    public static final String BANCO = "Banco";
    public static final String BANCO_DESCRICAO = "Operações de gestão de bancos";

    public static final String COLABORADORES = "Colaborador";
    public static final String COLABORADORES_DESCRICAO = "Operações de gestão de colaboradores";

    public static final String LOCAL_DE_ACESSO = "Local de Acesso";
    public static final String LOCAL_DE_ACESSO_DESCRICAO = "Operações de gestão de locais de acesso";

    public static final String DEPARTAMENTO = "Departamento";
    public static final String DEPARTAMENTO_DESCRICAO = "Operações de gestão de departamentos";

    public static final String CATEGORIA = "Categoria de Clientes";
    public static final String CATEGORIA_DESCRICAO = "Operações de gestão de categorias de clientes";

    public static final String CIDADE = "Cidade";
    public static final String CIDADE_DESCRICAO = "Operações de gestão de cidades";

    public static final String CLASSIFICACAO = "Classificação";
    public static final String CLASSIFICACAO_DESCRICAO = "Operações de gestão de classificações";

    public static final String CONTA_CORRENTE = "Conta Corrente";
    public static final String CONTA_CORRENTE_DESCRICAO = "Operações de gestão de contas correntes";

    public static final String NOTA_FISCAL = "Config. Nota Fiscal";
    public static final String NOTA_FISCAL_DESCRICAO = "Operações de gestão de notas fiscais";

    public static final String EMPRESA = "Empresa";
    public static final String EMPRESA_DESCRICAO = "Operações de gestão de empresas";

    public static final String ESTADO = "Estado";
    public static final String ESTADO_DESCRICAO = "Operações de gestão de estados";

    public static final String FERIADO = "Feriado";
    public static final String FERIADO_DESCRICAO = "Operações de gestão de feriados";

    public static final String GRAU_INSTRUCAO = "Grau de Instrução";
    public static final String GRAU_INSTRUCAO_DESCRICAO = "Operações de gestão de graus de instrução";

    public static final String GRUPO = "Grupo de Alunos";
    public static final String GRUPO_DESCRICAO = "Operações de gestão de grupos de alunos";

    public static final String HEALTH = "Health";
    public static final String HEALTH_DESCRICAO = "Operações de verificação de saúde do sistema";

    public static final String MALA_DIRETA = "Mala Direta";
    public static final String MALA_DIRETA_DESCRICAO = "Operações de gestão de campanhas de mala direta para comunicação com alunos da academia";

    public static final String CONFIGURACOES_SISTEMA = "Configurações de Cadastro Auxiliar";
    public static final String CONFIGURACOES_SISTEMA_DESCRICAO = "Operações de configurações gerais do sistema de cadastros auxiliares";

    public static final String IMPOSTO = "Imposto";
    public static final String IMPOSTO_DESCRICAO = "Operações de gestão de impostos de produtos e CFOP";

    public static final String INDICE_FINANCEIRO = "Índice Financeiro Reajuste Preços";
    public static final String INDICE_FINANCEIRO_DESCRICAO = "Operações de gestão de índices financeiros para reajuste de preços de planos de academia";

    public static final String JUSTIFICATIVA_OPERACAO = "Justificativa de Operação";
    public static final String JUSTIFICATIVA_OPERACAO_DESCRICAO = "Operações de gestão de justificativas para operações de academia";

    public static final String META_FINANCEIRA = "Meta Financeira";
    public static final String META_FINANCEIRA_DESCRICAO = "Operações de gestão de metas financeiras para academias e redes de academias";

    public static final String MODELO_CONTRATO = "Modelo de Contrato";
    public static final String MODELO_CONTRATO_DESCRICAO = "Operações de gestão de modelos de contrato para academias e redes de academias";

    public static final String OPERADORA_CARTAO = "Operadora de Cartão";
    public static final String OPERADORA_CARTAO_DESCRICAO = "Operações de gestão de operadoras de cartão para pagamentos de mensalidades e serviços da academia";

    public static final String PAIS = "País";
    public static final String PAIS_DESCRICAO = "Operações de gestão de países para academias e redes de academias";

    public static final String PARENTESCO = "Parentesco";
    public static final String PARENTESCO_DESCRICAO = "Operações de gestão de parentescos para relacionamentos familiares em academias";

    public static final String PERGUNTA = "Pergunta";
    public static final String PERGUNTA_DESCRICAO = "Operações de gestão de perguntas para questionários de avaliação física, saúde e satisfação em academias";

    public static final String PESQUISA_SATISFACAO = "Pesquisa de Satisfação / NPS";
    public static final String PESQUISA_SATISFACAO_DESCRICAO = "Operações de consulta de pesquisas de satisfação para avaliação da qualidade dos serviços da academia";

    public static final String PROFISSAO = "Profissão";
    public static final String PROFISSAO_DESCRICAO = "Operações de gestão de profissões para colaboradores e instrutores de academias";

    public static final String QUESTIONARIO = "Questionário";
    public static final String QUESTIONARIO_DESCRICAO = "Operações de gestão de questionários para avaliação física, pesquisas de satisfação e coleta de informações em academias";

     public static final String SERVIDOR_FACIAL = "Servidor Facial";
    public static final String SERVIDOR_FACIAL_DESCRICAO = "Operações de gestão de servidores de reconhecimento facial para controle de acesso de alunos, instrutores e colaboradores em academias";

    public static final String TAMANHO_ARMARIO = "Tamanho de Armário";
    public static final String TAMANHO_ARMARIO_DESCRICAO = "Operações de gestão de tamanhos de armários para vestiários de academias";

    public static final String USUARIO = "Usuário";
    public static final String USUARIO_DESCRICAO = "Operações de gestão de usuários do sistema de academias";
}
