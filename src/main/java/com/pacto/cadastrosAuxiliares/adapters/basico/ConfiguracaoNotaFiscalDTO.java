package com.pacto.cadastrosAuxiliares.adapters.basico;

import com.pacto.cadastrosAuxiliares.entities.basico.ConfiguracaoNotaFiscal;
import io.swagger.v3.oas.annotations.media.Schema;

public class ConfiguracaoNotaFiscalDTO {

    @Schema(description = "Código identificador único da configuração de nota fiscal", example = "1")
    private Integer codigo;

    @Schema(description = "Código da empresa/academia proprietária da configuração", example = "1")
    private Integer empresa;

    @Schema(description = "Tipo de nota fiscal configurada para a academia", example = "1")
    private Integer tipoNotaFiscal;

    @Schema(description = "Descrição da configuração de nota fiscal", example = "Configuração NFSe para serviços de personal training")
    private String descricao;

    public ConfiguracaoNotaFiscalDTO() {
    }

    public ConfiguracaoNotaFiscalDTO(ConfiguracaoNotaFiscal config) {
        this.codigo = config.getCodigo();
        this.empresa = config.getEmpresa().getCodigo();
        this.tipoNotaFiscal = config.getTipoNotafiscal();
        this.descricao = config.getDescricao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getTipoNotaFiscal() {
        return tipoNotaFiscal;
    }

    public void setTipoNotaFiscal(Integer tipoNotaFiscal) {
        this.tipoNotaFiscal = tipoNotaFiscal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
