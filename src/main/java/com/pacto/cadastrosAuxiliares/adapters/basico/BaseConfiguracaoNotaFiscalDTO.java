package com.pacto.cadastrosAuxiliares.adapters.basico;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

public class BaseConfiguracaoNotaFiscalDTO {

    @Schema(description = "Lista de configurações para emissão de Nota Fiscal de Serviço Eletrônica para academias")
    private List<ConfiguracaoNotaFiscalDTO>  listaConfigEmissaoNFSe;

    @Schema(description = "Lista de configurações para emissão de Nota Fiscal do Consumidor Eletrônica para vendas de produtos de academia")
    private List<ConfiguracaoNotaFiscalDTO> listaConfigEmissaoNFCe;


    public BaseConfiguracaoNotaFiscalDTO() {
        listaConfigEmissaoNFSe = new ArrayList<>();
        listaConfigEmissaoNFCe = new ArrayList<>();
    }

    public List<ConfiguracaoNotaFiscalDTO> getListaConfigEmissaoNFSe() {
        return listaConfigEmissaoNFSe;
    }

    public void setListaConfigEmissaoNFSe(List<ConfiguracaoNotaFiscalDTO> listaConfigEmissaoNFSe) {
        this.listaConfigEmissaoNFSe = listaConfigEmissaoNFSe;
    }

    public List<ConfiguracaoNotaFiscalDTO> getListaConfigEmissaoNFCe() {
        return listaConfigEmissaoNFCe;
    }

    public void setListaConfigEmissaoNFCe(List<ConfiguracaoNotaFiscalDTO> listaConfigEmissaoNFCe) {
        this.listaConfigEmissaoNFCe = listaConfigEmissaoNFCe;
    }
}
