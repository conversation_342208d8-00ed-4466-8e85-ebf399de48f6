package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.QuestionarioDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroQuestionarioJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.mscomunication.midiams.MediaMs;
import com.pacto.cadastrosAuxiliares.mscomunication.midiams.dto.MidiaDTO;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.cadastrosAuxiliares.services.interfaces.QuestionarioService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.questionario.ExemploRespostaChaveEmpresa;
import com.pacto.cadastrosAuxiliares.swagger.respostas.questionario.ExemploRespostaDownloadImagem;
import com.pacto.cadastrosAuxiliares.swagger.respostas.questionario.ExemploRespostaQuestionario;
import com.pacto.cadastrosAuxiliares.swagger.respostas.questionario.ExemploRespostaQuestionarioPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.questionario.ExemploRespostaUrlImagem;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/questionario")
@Tag(name = SwaggerTags.QUESTIONARIO, description = SwaggerTags.QUESTIONARIO_DESCRICAO)
public class QuestionarioController {
    private final QuestionarioService questionarioService;
    private final MediaMs mediaMs;
    private final RequestService requestService;

    public QuestionarioController(QuestionarioService questionarioService, MediaMs mediaMs, RequestService requestService) {
        this.questionarioService = questionarioService;
        this.mediaMs = mediaMs;
        this.requestService = requestService;
    }

    @Operation(
            summary = "Consultar questionários",
            description = "Consulta uma lista paginada de questionários disponíveis na academia, incluindo questionários de avaliação física, pesquisas de satisfação e questionários de saúde.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código identificador do questionário\n" +
                                    "- **nomeinterno**: Nome interno do questionário\n" +
                                    "- **titulopesquisa**: Título da pesquisa\n" +
                                    "- **ativo**: Status de ativação do questionário",
                            example = "nomeinterno,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome interno do questionário ou pelo código identificador",
                            example = "{\"quicksearchValue\":\"satisfação equipamentos\"}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaQuestionarioPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false)JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroQuestionarioJSON filtroQuestionarioJSON = new FiltroQuestionarioJSON(filtros);
            return ResponseEntityFactory.ok(questionarioService.findAll(filtroQuestionarioJSON, paginadorDTO), paginadorDTO);
        }  catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar questionário",
            description = "Consulta as informações detalhadas de um questionário específico pelo código identificador, incluindo todas as perguntas associadas e configurações.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador único do questionário que será consultado", example = "15", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaQuestionario.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> questionario(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(questionarioService.questionario(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Incluir questionário",
            description = "Inclui um novo questionário ou atualiza um questionário existente no sistema da academia. Permite configurar perguntas, textos de apresentação, cores e imagens de fundo.",
            tags = {SwaggerTags.QUESTIONARIO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do questionário que será incluído ou atualizado no sistema",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionarioDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaQuestionario.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirQuestionario(@RequestBody QuestionarioDTO questionario) {
        try {
            return ResponseEntityFactory.ok(questionarioService.saveOrUpdate(questionario));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }

    @Operation(
            summary = "Deletar questionário",
            description = "Remove um questionário específico do sistema da academia pelo código identificador. Esta ação também remove todas as perguntas associadas ao questionário.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador único do questionário que será removido", example = "15", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida"
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarQuestionario(@PathVariable Integer id) {
        try {
            questionarioService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Obter URL da imagem",
            description = "Obtém a URL completa de uma imagem de questionário através da chave de identificação da mídia no sistema.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(name = "imageKey", description = "Chave de identificação da imagem no sistema de mídia. Se não informada, retorna resposta vazia", example = "PESQUISA_IMG_1704067200000", required = false),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaUrlImagem.class)
                            )
                    )
            }
    )
    @GetMapping("/obter-url-imagem")
    public ResponseEntity<EnvelopeRespostaDTO> getUrlFromImageKey(@RequestParam(required = false) String imageKey) {
        try {
            if (imageKey == null) {
                return ResponseEntityFactory.ok();
            }
            return ResponseEntityFactory.ok(mediaMs.getImageUrl(imageKey));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("error_get_image_url", e.getMessage());
        }
    }

    @Operation(
            summary = "Download da imagem do questionário",
            description = "Realiza o download dos dados binários da imagem de fundo de um questionário específico através do código identificador.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador único do questionário cuja imagem será baixada", example = "15", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaDownloadImagem.class)
                            )
                    )
            }
    )
    @GetMapping("/dowload-imagem/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> dowloadImage(@PathVariable Integer id) {
        try {
            QuestionarioDTO questionarioDTO = questionarioService.questionario(id);
            MidiaDTO midiaDTO = new MidiaDTO();
            midiaDTO.setChave(this.requestService.getUsuarioAtual().getChave());
            midiaDTO.setIdentificador(questionarioDTO.getFundoimagem());
            midiaDTO.setTipo("PESQUISA");
            questionarioDTO.setMidia(midiaDTO);
            return ResponseEntityFactory.ok(mediaMs.downloadByteArray(questionarioDTO.getMidia()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("error_dowload_image", e.getMessage());
        }
    }

    @Operation(
            summary = "Gerar chave da empresa para imagem",
            description = "Gera uma chave única de identificação da empresa para upload de imagens de questionários no sistema de mídia.",
            tags = {SwaggerTags.QUESTIONARIO},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaChaveEmpresa.class)
                            )
                    )
            }
    )
    @GetMapping("/gen-empresa-imagem")
    public ResponseEntity<EnvelopeRespostaDTO> getEmpresaChave(){
        try {
            return ResponseEntityFactory.ok(mediaMs.genEmpresaKey(requestService.getUsuarioAtual().getChave(), requestService.getEmpresaId().toString()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("error_dowload_image", e.getMessage());
        }
    }
}
