package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.EstadoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroEstadoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.EstadoService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.estado.ExemploRespostaEstado;
import com.pacto.cadastrosAuxiliares.swagger.respostas.estado.ExemploRespostaListEstado;
import com.pacto.cadastrosAuxiliares.swagger.respostas.estado.ExemploRespostaListEstadoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/estados")
public class EstadoController {
    private final EstadoService estadoService;

    public EstadoController(EstadoService estadoService) {
        this.estadoService = estadoService;
    }

    @Operation(
            summary = "Consultar estados",
            description = "Consulta uma lista paginada de estados cadastrados no sistema de academias, com possibilidade de filtros por descrição, código ou país.",
            tags = {SwaggerTags.ESTADO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código identificador do estado\n" +
                                    "- **descricao**: Nome do estado\n" +
                                    "- **sigla**: Sigla do estado\n" +
                                    "- **codigoIBGE**: Código IBGE do estado\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome ou pelo código do estado\n" +
                                    "- **pais**: Filtra pelos estados de um país específico",
                            example = "{\"quicksearchValue\":\"São Paulo\",\"pais\":1}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListEstadoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroEstadoJSON filtroEstadoJSON = new FiltroEstadoJSON(filtros);
            return ResponseEntityFactory.ok(estadoService.findAll(filtroEstadoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar estados por país",
            description = "Consulta uma lista de estados que pertencem a um país específico, com possibilidade de filtro por descrição do estado.",
            tags = {SwaggerTags.ESTADO},
            parameters = {
                    @Parameter(name = "idPais", description = "Código identificador do país para buscar seus estados", example = "1", required = true),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome do estado",
                            example = "{\"quicksearchValue\":\"São Paulo\"}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListEstado.class)
                            )
                    )
            }
    )
    @GetMapping("/find-by-pais/{idPais}")
    public ResponseEntity<EnvelopeRespostaDTO> findByIdPais(@RequestParam(value = "filters", required = false) JSONObject filtros, @PathVariable Integer idPais){

        try{
            FiltroEstadoJSON filtroEstadoJSON = new FiltroEstadoJSON((filtros));
            return ResponseEntityFactory.ok(estadoService.findByIdPais(filtroEstadoJSON, idPais));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar estado",
            description = "Consulta as informações detalhadas de um estado específico pelo seu código identificador.",
            tags = {SwaggerTags.ESTADO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do estado que será consultado", example = "35", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaEstado.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> estado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(estadoService.estado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir ou atualizar estado",
            description = "Inclui um novo estado ou atualiza um estado existente no sistema de academias. Se o código for informado, será feita uma atualização; caso contrário, será criado um novo estado.",
            tags = {SwaggerTags.ESTADO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do estado a ser incluído ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = EstadoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaEstado.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirEstado(@RequestBody EstadoDTO estadoDTO) {
        try {
            return ResponseEntityFactory.ok(estadoService.saveOrUpdate(estadoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }

    @Operation(
            summary = "Deletar estado",
            description = "Remove um estado do sistema de academias pelo seu código identificador.",
            tags = {SwaggerTags.ESTADO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do estado que será deletado", example = "35", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida"
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarPlano(@PathVariable Integer id ) {
        try {
            estadoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Verificar vínculo com cidade",
            description = "Verifica se o estado possui vínculo com alguma cidade cadastrada no sistema de academias antes de permitir operações como exclusão.",
            tags = {SwaggerTags.ESTADO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do estado que será verificado", example = "35", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida"
                    )
            }
    )
    @GetMapping("/verifica-vinculo-cidade/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> verificaVinculoCidade(@PathVariable Integer id ) {
        try {
            estadoService.existeVinculoComCidade(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

}
