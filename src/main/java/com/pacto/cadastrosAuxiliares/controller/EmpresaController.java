package com.pacto.cadastrosAuxiliares.controller;


import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroEmpresaJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.EmpresaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.empresa.ExemploRespostaEmpresa;
import com.pacto.cadastrosAuxiliares.swagger.respostas.empresa.ExemploRespostaListEmpresa;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/empresas")
public class EmpresaController {
    private final EmpresaService empresaService;

    public EmpresaController(EmpresaService empresaService) {
        this.empresaService = empresaService;
    }

    @Operation(
            summary = "Consultar empresas",
            description = "Consulta uma lista de empresas com possibilidade de aplicar filtros de busca.",
            tags = {SwaggerTags.EMPRESA},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome da empresa\n" +
                                    "- **codigo**: Filtra pelo código específico da empresa\n" +
                                    "- **ativa**: Filtra por empresas ativas (true/false)",
                            example = "{\"quicksearchValue\":\"Academia Pacto\",\"codigo\":1,\"ativa\":true}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListEmpresa.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false)JSONObject filtros) {
        try {
            FiltroEmpresaJSON filtroEmpresaJSON = new FiltroEmpresaJSON(filtros);
            return ResponseEntityFactory.ok(empresaService.findAll(filtroEmpresaJSON));
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar empresa",
            description = "Consulta as informações de uma empresa específica pelo código identificador.",
            tags = {SwaggerTags.EMPRESA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da empresa que será consultada", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaEmpresa.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findEmpresaById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(empresaService.findEmpresaById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
