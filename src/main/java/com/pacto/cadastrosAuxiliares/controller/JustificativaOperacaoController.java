package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.JustificativaOperacaoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroJustificativaOperacaoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.JustificativaOperacaoService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.justificativaoperacao.ExemploRespostaJustificativaOperacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.justificativaoperacao.ExemploRespostaListJustificativaOperacaoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/justificativa-operacao")

public class JustificativaOperacaoController {

    private final JustificativaOperacaoService justificativaOperacaoService;

    public JustificativaOperacaoController(JustificativaOperacaoService justificativaOperacaoService) {
        this.justificativaOperacaoService = justificativaOperacaoService;
    }

    @Operation(
            summary = "Criar ou atualizar justificativa de operação",
            description = "Cria uma nova justificativa de operação ou atualiza uma existente. As justificativas são utilizadas para operações como atestados médicos, cancelamentos, trancamentos e outras situações especiais na academia.",
            tags = {SwaggerTags.JUSTIFICATIVA_OPERACAO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da justificativa de operação a ser criada ou atualizada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = JustificativaOperacaoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaJustificativaOperacao.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody JustificativaOperacaoDTO justificativaOperacaoDTO){
        try{
            return  ResponseEntityFactory.ok(justificativaOperacaoService.saveOrUpdate(justificativaOperacaoDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar justificativas de operação",
            description = "Consulta justificativas de operação com paginação e filtros. Permite buscar por descrição e filtrar por tipo de operação específico.",
            tags = {SwaggerTags.JUSTIFICATIVA_OPERACAO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código da justificativa\n" +
                                    "- **descricao**: Descrição da justificativa\n" +
                                    "- **nomeEmpresa**: Nome da empresa\n" +
                                    "- **tipoOperacao**: Tipo de operação\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição da justificativa de operação\n" +
                                    "- **tipoOperacao**: Filtra pelo tipo de operação (AT, CA, CR, TR, BO)",
                            example = "{\"quicksearchValue\":\"atestado\",\"tipoOperacao\":\"AT\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListJustificativaOperacaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
        @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroJustificativaOperacaoJSON filtrosJson = new FiltroJustificativaOperacaoJSON(filtros);
            return ResponseEntityFactory.ok(justificativaOperacaoService.findAll(filtrosJson, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar justificativa de operação",
            description = "Consulta as informações de uma justificativa de operação pelo código identificador dela.",
            tags = {SwaggerTags.JUSTIFICATIVA_OPERACAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da justificativa de operação que será consultada", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaJustificativaOperacao.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(justificativaOperacaoService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Excluir justificativa de operação",
            description = "Exclui uma justificativa de operação pelo código identificador dela.",
            tags = {SwaggerTags.JUSTIFICATIVA_OPERACAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da justificativa de operação que será excluída", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id ) {
        try {
            justificativaOperacaoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}

