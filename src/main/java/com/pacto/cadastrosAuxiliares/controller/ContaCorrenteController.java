package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.ContaCorrenteDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroContaCorrenteJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.ContaCorrenteService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.contacorrente.ExemploRespostaContaCorrente;
import com.pacto.cadastrosAuxiliares.swagger.respostas.contacorrente.ExemploRespostaListContaCorrentePaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/contasCorrentes")

public class ContaCorrenteController {

    private final ContaCorrenteService contaCorrenteService;

    public ContaCorrenteController(ContaCorrenteService contaCorrenteService) {
        this.contaCorrenteService = contaCorrenteService;
    }

    @Operation(
            summary = "Incluir conta corrente",
            description = "Inclui ou atualiza uma conta corrente no sistema.",
            tags = {SwaggerTags.CONTA_CORRENTE},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da conta corrente que será incluída ou atualizada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ContaCorrenteDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaContaCorrente.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirContaCorrente(@RequestBody ContaCorrenteDTO contaCorrenteDTO){
        try{
            return  ResponseEntityFactory.ok(contaCorrenteService.saveOrUpdate(contaCorrenteDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar contas correntes",
            description = "Consulta uma lista paginada de contas correntes com possibilidade de aplicar filtros de busca.",
            tags = {SwaggerTags.CONTA_CORRENTE},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código da conta corrente\n" +
                                    "- **agencia**: Número da agência\n" +
                                    "- **agenciaDv**: Dígito verificador da agência\n" +
                                    "- **contaCorrente**: Número da conta corrente\n" +
                                    "- **contaCorrenteDv**: Dígito verificador da conta corrente\n" +
                                    "- **nomeBanco**: Nome do banco\n",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome do banco ou código da conta corrente\n" +
                                    "- **codigoEmpresa**: Filtra pelas contas correntes associadas a uma empresa específica\n" +
                                    "- **contaCorrente**: Filtra pelo número da conta corrente",
                            example = "{\"quicksearchValue\":\"Banco do Brasil\",\"codigoEmpresa\":1}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListContaCorrentePaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
        @Parameter(hidden = true) PaginadorDTO paginadorDTO) {

        try {
            FiltroContaCorrenteJSON filtroContaCorrenteJSON = new FiltroContaCorrenteJSON(filtros);
            return ResponseEntityFactory.ok(contaCorrenteService.findAll(filtroContaCorrenteJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar conta corrente",
            description = "Consulta as informações de uma conta corrente pelo código identificador dela.",
            tags = {SwaggerTags.CONTA_CORRENTE},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da conta corrente que será consultada", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaContaCorrente.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(contaCorrenteService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Excluir conta corrente",
            description = "Exclui uma conta corrente do sistema pelo código identificador dela.",
            tags = {SwaggerTags.CONTA_CORRENTE},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da conta corrente que será excluída", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id ) {
        try {
            contaCorrenteService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}

