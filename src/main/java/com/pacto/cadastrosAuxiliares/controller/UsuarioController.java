package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.services.interfaces.UsuarioService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.usuario.ExemploRespostaUsuario;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriUtils;

import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/usuario")
@Tag(name = SwaggerTags.USUARIO, description = SwaggerTags.USUARIO_DESCRICAO)
public class UsuarioController {

    @Autowired
    UsuarioService usuarioService;

    @Operation(
            summary = "Consultar usuário por nome de usuário",
            description = "Consulta as informações básicas de um usuário do sistema da academia através do seu nome de usuário (username).",
            tags = {SwaggerTags.USUARIO},
            parameters = {
                    @Parameter(name = "username", description = "Nome de usuário para busca no sistema da academia", example = "carlos.instrutor", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaUsuario.class)
                            )
                    )
            }
    )
    @GetMapping("/{username}")
    public ResponseEntity<EnvelopeRespostaDTO> findByUsername(@PathVariable String username) {
        try {
            String decodedUsername = UriUtils.decode(username, StandardCharsets.UTF_8);
            return ResponseEntityFactory.ok(usuarioService.buscarUsuarioDestino(decodedUsername));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
