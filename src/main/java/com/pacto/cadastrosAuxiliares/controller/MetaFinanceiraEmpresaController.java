package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.MetaFinanceiraEmpresaDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroMetaAtingidaJSON;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroMetaFinanceiraEmpresaJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.MetaFinanceiraEmpresaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.respostas.metafinanceira.ExemploRespostaListMetaFinanceiraEmpresaPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.metafinanceira.ExemploRespostaMetaAtingida;
import com.pacto.cadastrosAuxiliares.swagger.respostas.metafinanceira.ExemploRespostaMetaFinanceiraEmpresa;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/metas-financeiras")

public class MetaFinanceiraEmpresaController {

    private final MetaFinanceiraEmpresaService metaFinanceiraEmpresaService;

    public MetaFinanceiraEmpresaController(MetaFinanceiraEmpresaService metaFinanceiraEmpresaService) {
        this.metaFinanceiraEmpresaService = metaFinanceiraEmpresaService;
    }

    @Operation(
            summary = "Consultar metas financeiras de academias",
            description = "Consulta uma lista paginada de metas financeiras de academias com filtros opcionais.",
            tags = {SwaggerTags.META_FINANCEIRA},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código da meta financeira\n" +
                                    "- **empresa**: Nome da academia\n" +
                                    "- **mes**: Mês da meta\n" +
                                    "- **ano**: Ano da meta\n" +
                                    "- **anoMes**: Período da meta (ano/mês)\n" +
                                    "- **descricao**: Descrição da meta\n" +
                                    "- **meta1**: Primeira meta de faturamento\n" +
                                    "- **meta2**: Segunda meta de faturamento\n" +
                                    "- **meta3**: Terceira meta de faturamento\n" +
                                    "- **meta4**: Quarta meta de faturamento\n" +
                                    "- **meta5**: Quinta meta de faturamento\n",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição ou código da meta financeira\n" +
                                    "- **codigoEmpresa**: Filtra pelo código da academia\n" +
                                    "- **dataInicial**: Data inicial para filtro por período (timestamp)\n" +
                                    "- **dataFinal**: Data final para filtro por período (timestamp)",
                            example = "{\"quicksearchValue\":\"meta março\",\"codigoEmpresa\":1}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListMetaFinanceiraEmpresaPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
        @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroMetaFinanceiraEmpresaJSON filtrosMfe = new FiltroMetaFinanceiraEmpresaJSON(filtros);
            return ResponseEntityFactory.ok(metaFinanceiraEmpresaService.findAll(filtrosMfe, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar valor da meta atingida",
            description = "Consulta o valor da meta financeira atingida por uma academia em um período específico.",
            tags = {SwaggerTags.META_FINANCEIRA},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **empresa**: Código da academia para consulta da meta atingida\n" +
                                    "- **dataInicial**: Data inicial do período para consulta (timestamp)\n" +
                                    "- **dataFinal**: Data final do período para consulta (timestamp)",
                            example = "{\"empresa\":1,\"dataInicial\":1672531200000,\"dataFinal\":1675209599000}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaMetaAtingida.class)
                            )
                    )
            }
    )
    @GetMapping("/meta-atingida")
    public ResponseEntity<EnvelopeRespostaDTO> consultarMetaAtingida(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroMetaAtingidaJSON filtrosMetaAtingida = new FiltroMetaAtingidaJSON(filtros);
            return ResponseEntityFactory.ok(metaFinanceiraEmpresaService.consultarMetaAtingida(filtrosMetaAtingida));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Deletar meta financeira",
            description = "Remove uma meta financeira de academia do sistema pelo código identificador.",
            tags = {SwaggerTags.META_FINANCEIRA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da meta financeira que será removida", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Meta financeira removida com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id ) {
        try {
            metaFinanceiraEmpresaService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Salvar meta financeira de academia",
            description = "Cria uma nova meta financeira ou atualiza uma meta existente para uma academia.",
            tags = {SwaggerTags.META_FINANCEIRA},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da meta financeira da academia que será salva ou atualizada",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = MetaFinanceiraEmpresaDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Meta financeira salva com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaMetaFinanceiraEmpresa.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> salvarMetaFinanceiraEmpresa(@RequestBody MetaFinanceiraEmpresaDTO metaFinanceiraEmpresaDTO){
        try{
            return  ResponseEntityFactory.ok(metaFinanceiraEmpresaService.saveOrUpdate(metaFinanceiraEmpresaDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar meta financeira",
            description = "Consulta as informações de uma meta financeira de academia pelo código identificador.",
            tags = {SwaggerTags.META_FINANCEIRA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da meta financeira que será consultada", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaMetaFinanceiraEmpresa.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(metaFinanceiraEmpresaService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}

