package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.PerguntaDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroPerguntaJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.PerguntaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.pergunta.ExemploRespostaPergunta;
import com.pacto.cadastrosAuxiliares.swagger.respostas.pergunta.ExemploRespostaPerguntaPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.pergunta.ExemploRespostaListPerguntaCodNome;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pergunta")
public class PerguntaController {
    private final PerguntaService perguntaService;

    public PerguntaController(PerguntaService perguntaService) {
        this.perguntaService = perguntaService;
    }

    @Operation(
            summary = "Consultar perguntas",
            description = "Consulta uma lista paginada de perguntas utilizadas em questionários de avaliação física, saúde e satisfação da academia.",
            tags = {SwaggerTags.PERGUNTA},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição da pergunta ou pelo código identificador",
                            example = "{\"quicksearchValue\":\"lesão\"}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaPerguntaPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO){
        try{
            FiltroPerguntaJSON filtroPerguntaJSON = new FiltroPerguntaJSON(filtros);
            return ResponseEntityFactory.ok(perguntaService.findAll(filtroPerguntaJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for ( StackTraceElement stackTraceElement : trace ) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar pergunta",
            description = "Consulta as informações detalhadas de uma pergunta específica pelo código identificador, incluindo suas opções de resposta.",
            tags = {SwaggerTags.PERGUNTA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da pergunta que será consultada", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaPergunta.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public  ResponseEntity<EnvelopeRespostaDTO> pais(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(perguntaService.pergunta(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar perguntas simplificadas",
            description = "Consulta uma lista simplificada de perguntas contendo apenas código e descrição, útil para seleção em formulários de questionários.",
            tags = {SwaggerTags.PERGUNTA},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição da pergunta",
                            example = "{\"quicksearchValue\":\"objetivo\"}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListPerguntaCodNome.class)
                            )
                    )
            }
    )
    @GetMapping("/only-cod-name")
    public ResponseEntity<EnvelopeRespostaDTO> findAllCodName(@RequestParam(value = "filters", required = false)JSONObject filtros) {
        try {
            FiltroPerguntaJSON filtroPerguntaJSON = new FiltroPerguntaJSON(filtros);
            return ResponseEntityFactory.ok(perguntaService.findAllCodName(filtroPerguntaJSON));
        }  catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Incluir pergunta",
            description = "Inclui uma nova pergunta ou atualiza uma pergunta existente no sistema de questionários da academia.",
            tags = {SwaggerTags.PERGUNTA},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da pergunta que será incluída ou atualizada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = PerguntaDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaPergunta.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirPergunta(@RequestBody PerguntaDTO pergunta) {
        try {
            return ResponseEntityFactory.ok(perguntaService.saveOrUpdate(pergunta));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }   catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }

    @Operation(
            summary = "Deletar pergunta",
            description = "Remove uma pergunta do sistema de questionários da academia pelo código identificador.",
            tags = {SwaggerTags.PERGUNTA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da pergunta que será removida", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarPergunta(@PathVariable Integer id ) {
        try {
            perguntaService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

}
