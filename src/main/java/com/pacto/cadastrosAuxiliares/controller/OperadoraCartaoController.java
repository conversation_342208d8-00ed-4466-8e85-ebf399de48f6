package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.OperadoraCartaoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroOperadoraCartaoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.OperadoraCartaoService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.respostas.operadoracartao.ExemploRespostaListOperadoraCartaoPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.operadoracartao.ExemploRespostaOperadoraCartao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/operadoracartao")
public class OperadoraCartaoController {

    private final OperadoraCartaoService service;

    public OperadoraCartaoController(OperadoraCartaoService service) {
        this.service = service;
    }

    @Operation(
            summary = "Consultar operadoras de cartão",
            description = "Consulta uma lista paginada de operadoras de cartão disponíveis para pagamentos de mensalidades e serviços da academia.",
            tags = {SwaggerTags.OPERADORA_CARTAO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código da operadora\n" +
                                    "- **descricao**: Nome da operadora\n" +
                                    "- **codigoOperadora**: Código identificador da operadora\n" +
                                    "- **credito**: Se aceita crédito\n" +
                                    "- **qtdeMaxParcelas**: Quantidade máxima de parcelas\n" +
                                    "- **taxa**: Taxa de processamento\n" +
                                    "- **ativo**: Status ativo/inativo\n" +
                                    "- **padraoRecebimento**: Se é padrão para recebimento",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome da operadora, código da operadora ou código identificador\n" +
                                    "- **ativo**: Filtra operadoras ativas ou inativas. Valores: [\"true\"] para ativas, [\"false\"] para inativas",
                            example = "{\"quicksearchValue\":\"visa\",\"ativo\":[\"true\"]}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListOperadoraCartaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroOperadoraCartaoJSON filtro = new FiltroOperadoraCartaoJSON(filtros);
            return ResponseEntityFactory.ok(service.findAll(filtro, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar operadora de cartão",
            description = "Consulta as informações detalhadas de uma operadora de cartão específica pelo código identificador.",
            tags = {SwaggerTags.OPERADORA_CARTAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da operadora de cartão que será consultada", example = "15", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaOperadoraCartao.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> operadoracartao(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.operadoraCartao(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Criar ou atualizar operadora de cartão",
            description = "Cria uma nova operadora de cartão ou atualiza uma existente para uso em pagamentos de mensalidades e serviços da academia.",
            tags = {SwaggerTags.OPERADORA_CARTAO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da operadora de cartão que será criada ou atualizada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = OperadoraCartaoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaOperadoraCartao.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> inclusao(@RequestBody OperadoraCartaoDTO dto) {
        try {
            return ResponseEntityFactory.ok(service.saveOrUpdate(dto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }

    @Operation(
            summary = "Remover operadora de cartão",
            description = "Remove uma operadora de cartão do sistema da academia pelo código identificador.",
            tags = {SwaggerTags.OPERADORA_CARTAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da operadora de cartão que será removida", example = "15", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id ) {
        try {
            service.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
