package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.ModeloContratoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroModeloContratoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.ModeloContratoService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato.ExemploRespostaBoolean;
import com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato.ExemploRespostaListModeloContrato;
import com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato.ExemploRespostaListModeloContratoPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato.ExemploRespostaModeloContrato;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/modelo-contrato")
public class ModeloContratoController {

    private final ModeloContratoService modeloContratoService;

    public ModeloContratoController(ModeloContratoService modeloContratoService) {
        this.modeloContratoService = modeloContratoService;
    }

    @Operation(
            summary = "Consultar modelos de contrato",
            description = "Consulta uma lista paginada de modelos de contrato da academia com possibilidade de filtros de busca.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código do modelo de contrato\n" +
                                    "- **descricao**: Descrição do modelo de contrato\n" +
                                    "- **dataDefinicao**: Data de definição do modelo\n" +
                                    "- **situacao**: Situação do modelo\n" +
                                    "- **tipoContrato**: Tipo do contrato\n" +
                                    "- **nomeResponsavel**: Nome do responsável pela definição\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição do modelo, nome do responsável, data de definição (formato dd/MM/yyyy) ou código do modelo",
                            example = "{\"quicksearchValue\":\"contrato mensal\"}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListModeloContratoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(
                    @RequestParam(value = "filters", required = false) JSONObject filtros,
                    @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroModeloContratoJSON filtroModeloContratoJSON = new FiltroModeloContratoJSON(filtros);
            return ResponseEntityFactory.ok(modeloContratoService.findAll(filtroModeloContratoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar modelo de contrato",
            description = "Consulta as informações detalhadas de um modelo de contrato específico pelo código identificador.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do modelo de contrato que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaModeloContrato.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(modeloContratoService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Criar ou atualizar modelo de contrato",
            description = "Cria um novo modelo de contrato ou atualiza um modelo existente. Se o código for informado, será feita uma atualização; caso contrário, será criado um novo modelo.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do modelo de contrato a ser criado ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ModeloContratoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaModeloContrato.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody ModeloContratoDTO modeloContratoDTO) {
        try {
            return ResponseEntityFactory.ok(modeloContratoService.saveOrUpdate(modeloContratoDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Excluir modelo de contrato",
            description = "Exclui um modelo de contrato específico pelo código identificador. A exclusão só será permitida se o modelo não estiver sendo utilizado por contratos ativos.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do modelo de contrato que será excluído", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id ) {
        try {
            modeloContratoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Verificar termo de responsabilidade",
            description = "Verifica se o termo de responsabilidade está ativo nas configurações do sistema da academia.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaBoolean.class)
                            )
                    )
            }
    )
    @GetMapping("/isTermoResponsabilidade")
    public ResponseEntity<EnvelopeRespostaDTO> isTermoResponsabilidade() {
        try {
            return ResponseEntityFactory.ok(modeloContratoService.isTermoResponsabilidade());
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar modelos por situação e tipo",
            description = "Consulta modelos de contrato filtrados por situação e tipo de contrato específicos.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "situacao", description = "Situação do modelo de contrato (AT = Ativo, IN = Inativo)", example = "AT", required = true),
                    @Parameter(name = "tipoContrato", description = "Tipo do contrato (PL = Plano, SE = Serviço, CC = Comprovante de Compra)", example = "PL", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListModeloContrato.class)
                            )
                    )
            }
    )
    @GetMapping("/find-by-situacao-tipo-contrato/{situacao}/{tipoContrato}")
    public ResponseEntity<EnvelopeRespostaDTO> findBySituacaoTipoContrato(@PathVariable String situacao, @PathVariable String tipoContrato) {
        try {
            return ResponseEntityFactory.ok(modeloContratoService.findBySituacaoTipoContrato(situacao, tipoContrato, null));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar modelo por descrição",
            description = "Consulta um modelo de contrato específico pela descrição. Se não encontrar um modelo com a descrição informada, retorna um modelo vazio.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados contendo a descrição do modelo de contrato a ser consultado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ModeloContratoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaModeloContrato.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "/descricao", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findDescription(@RequestBody ModeloContratoDTO modeloContratoDTO) {
        try {
            return ResponseEntityFactory.ok(modeloContratoService.findDescription(modeloContratoDTO.getDescricao()));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
