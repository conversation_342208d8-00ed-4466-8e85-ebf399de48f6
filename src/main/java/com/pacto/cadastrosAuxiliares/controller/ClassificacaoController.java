package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.ClassificacaoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroClassificacaoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.ClassificacaoService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.classificacao.ExemploRespostaClassificacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.classificacao.ExemploRespostaListClassificacaoPaginacao;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/classificacao")
public class ClassificacaoController {

    private final ClassificacaoService classificacaoService;

    public ClassificacaoController(ClassificacaoService classificacaoService) {
        this.classificacaoService = classificacaoService;
    }

    @Operation(
            summary = "Consultar classificações",
            description = "Consulta uma lista paginada de classificações com possibilidade de aplicar filtros de busca.",
            tags = {SwaggerTags.CLASSIFICACAO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "nome,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome ou pelo código da classificação",
                            example = "{\"quicksearchValue\":\"empresarial\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListClassificacaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true ) PaginadorDTO paginadorDTO) {
        try {
            FiltroClassificacaoJSON filtroClassificacaoJSON = new FiltroClassificacaoJSON(filtros);
            return ResponseEntityFactory.ok(classificacaoService.findAll(filtroClassificacaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar classificação",
            description = "Consulta as informações de uma classificação pelo código identificador dela.",
            tags = {SwaggerTags.CLASSIFICACAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da classificação que será consultada", example = "12", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaClassificacao.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> classificacao(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(classificacaoService.classificacao(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir ou atualizar classificação",
            description = "Inclui uma nova classificação ou atualiza uma classificação existente. Se o código for informado, será feita uma atualização; caso contrário, será feita uma inclusão.",
            tags = {SwaggerTags.CLASSIFICACAO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da classificação para inclusão ou atualização",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ClassificacaoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaClassificacao.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirClassificacao(@RequestBody ClassificacaoDTO classificacao) {
        try {
            return ResponseEntityFactory.ok(classificacaoService.saveOrUpdate(classificacao));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @Operation(
            summary = "Deletar classificação",
            description = "Remove uma classificação do sistema pelo código identificador dela.",
            tags = {SwaggerTags.CLASSIFICACAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da classificação que será removida", example = "12", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarClassificacao(@PathVariable Integer id ) {
        try {
            classificacaoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
