package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.GrauInstrucaoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroGrauInstrucaoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.GrauInstrucaoService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaGrauInstrucao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaListGrauInstrucaoPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaVazia;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grau-instrucao")

public class GrauInstrucaoController {
    private final GrauInstrucaoService grauInstrucaoService;

    public GrauInstrucaoController(GrauInstrucaoService grauInstrucaoService) {
        this.grauInstrucaoService = grauInstrucaoService;
    }

    @Operation(
            summary = "Consultar graus de instrução",
            description = "Consulta uma lista paginada de graus de instrução com possibilidade de filtros e ordenação.",
            tags = {SwaggerTags.GRAU_INSTRUCAO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Ordena pelo código do grau de instrução\n" +
                                    "- **descricao**: Ordena pela descrição do grau de instrução\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição ou pelo código do grau de instrução",
                            example = "{\"quicksearchValue\":\"superior\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListGrauInstrucaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroGrauInstrucaoJSON filtroGrauInstrucaoJSON = new FiltroGrauInstrucaoJSON(filtros);
            return ResponseEntityFactory.ok(grauInstrucaoService.findAll(filtroGrauInstrucaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar grau de instrução",
            description = "Consulta as informações de um grau de instrução pelo código identificador dele.",
            tags = {SwaggerTags.GRAU_INSTRUCAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do grau de instrução que será consultado", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaGrauInstrucao.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> grauInstrucao(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(grauInstrucaoService.grauInstrucao(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir ou alterar grau de instrução",
            description = "Inclui um novo grau de instrução ou altera um grau de instrução existente. Se o código for informado, será feita a alteração; caso contrário, será feita a inclusão.",
            tags = {SwaggerTags.GRAU_INSTRUCAO},
            requestBody = @RequestBody(
                    description = "Dados do grau de instrução que será incluído ou alterado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = GrauInstrucaoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaGrauInstrucao.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirGrauInstrucao(@org.springframework.web.bind.annotation.RequestBody GrauInstrucaoDTO grauInstrucao) {
        try {
            return ResponseEntityFactory.ok(grauInstrucaoService.saveOrUpdate(grauInstrucao));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @Operation(
            summary = "Deletar grau de instrução",
            description = "Remove um grau de instrução do sistema pelo código identificador dele.",
            tags = {SwaggerTags.GRAU_INSTRUCAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do grau de instrução que será removido", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarGrauInstrucao(@PathVariable Integer id ) {
        try {
            grauInstrucaoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

}
