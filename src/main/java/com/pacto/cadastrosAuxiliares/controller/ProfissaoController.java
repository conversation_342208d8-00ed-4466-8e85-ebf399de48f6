package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.ProfissaoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroProfissaoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.ProfissaoService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.profissao.ExemploRespostaListProfissao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.profissao.ExemploRespostaListProfissaoPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.profissao.ExemploRespostaProfissao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/profissao")
public class ProfissaoController {
    private final ProfissaoService profissaoService;

    public ProfissaoController(ProfissaoService profissaoService) {
        this.profissaoService = profissaoService;
    }

    @Operation(
            summary = "Consultar profissões",
            description = "Consulta profissões de colaboradores e instrutores de academias com paginação e filtros de busca.",
            tags = {SwaggerTags.PROFISSAO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código da profissão\n" +
                                    "- **descricao**: Nome da profissão\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome da profissão ou pelo código da profissão",
                            example = "{\"quicksearchValue\":\"educador\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListProfissaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroProfissaoJSON filtroProfissaoJSON = new FiltroProfissaoJSON(filtros);
            return ResponseEntityFactory.ok(profissaoService.findAll(filtroProfissaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar profissão",
            description = "Consulta as informações de uma profissão específica pelo código identificador dela.",
            tags = {SwaggerTags.PROFISSAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da profissão que será consultada", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaProfissao.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> profissao(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(profissaoService.profissao(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir ou atualizar profissão",
            description = "Inclui uma nova profissão ou atualiza uma profissão existente no sistema da academia.",
            tags = {SwaggerTags.PROFISSAO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da profissão que será incluída ou atualizada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ProfissaoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaProfissao.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirProfissao(@RequestBody ProfissaoDTO profissao) {
        try {
            return ResponseEntityFactory.ok(profissaoService.saveOrUpdate(profissao));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @Operation(
            summary = "Deletar profissão",
            description = "Remove uma profissão do sistema da academia pelo código identificador dela.",
            tags = {SwaggerTags.PROFISSAO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da profissão que será removida", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida"
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarProfissao(@PathVariable Integer id ) {
        try {
            profissaoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar todas as profissões",
            description = "Consulta todas as profissões disponíveis no sistema da academia sem paginação.",
            tags = {SwaggerTags.PROFISSAO},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListProfissao.class)
                            )
                    )
            }
    )
    @GetMapping("/all")
    public ResponseEntity<EnvelopeRespostaDTO> todos() {
        try {
            return ResponseEntityFactory.ok(profissaoService.all());
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }
}
