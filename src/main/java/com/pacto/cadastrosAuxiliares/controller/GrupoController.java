package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.GrupoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroGrupoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.GrupoService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grupo.ExemploRespostaGrupo;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grupo.ExemploRespostaListGrupoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grupo")
public class GrupoController {
    private final GrupoService grupoService;

    public GrupoController(GrupoService grupoService) {
        this.grupoService = grupoService;
    }

    @Operation(
            summary = "Cadastrar grupo de alunos",
            description = "Cadastra um novo grupo de alunos ou atualiza um grupo existente na academia.",
            tags = {SwaggerTags.GRUPO},
            requestBody = @RequestBody(
                    description = "Informações do grupo de alunos que será cadastrado ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = GrupoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaGrupo.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirGrupo(@org.springframework.web.bind.annotation.RequestBody GrupoDTO grupos){
        try{
            return  ResponseEntityFactory.ok(grupoService.saveOrUpdate(grupos));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar grupos de alunos",
            description = "Consulta os grupos de alunos cadastrados na academia, podendo aplicar filtros para a consulta.",
            tags = {SwaggerTags.GRUPO},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome ou código do grupo de alunos\n" +
                                    "- **grupoInativo**: Filtra grupos ativos ou inativos (true/false)",
                            example = "{\"quicksearchValue\":\"musculação\",\"grupoInativo\":\"false\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código do grupo\n" +
                                    "- **descricao**: Nome do grupo\n" +
                                    "- **quantidadeMinimaAluno**: Quantidade mínima de alunos\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListGrupoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false)JSONObject filtros,
        @Parameter(hidden = true) PaginadorDTO paginadorDTO) {

        try {
            FiltroGrupoJSON filtroGrupoJSON = new FiltroGrupoJSON(filtros);
            return ResponseEntityFactory.ok(grupoService.findAll(filtroGrupoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } }

    @Operation(
            summary = "Consultar grupo de alunos",
            description = "Consulta as informações de um grupo de alunos específico pelo código identificador.",
            tags = {SwaggerTags.GRUPO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do grupo de alunos que será consultado", example = "15", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaGrupo.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> grupo(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(grupoService.grupo(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Excluir grupo de alunos",
            description = "Exclui um grupo de alunos específico do sistema da academia.",
            tags = {SwaggerTags.GRUPO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do grupo de alunos que será excluído", example = "15", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id ) {
        try {
            grupoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
