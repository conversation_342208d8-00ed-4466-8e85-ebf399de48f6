package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroCupomFiscalJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.CupomFiscalService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.cupomfiscal.ExemploRespostaCupomFiscal;
import com.pacto.cadastrosAuxiliares.swagger.respostas.cupomfiscal.ExemploRespostaListCupomFiscalPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cupomfiscal")
public class CupomFiscalController {

    private final CupomFiscalService cupomFiscalService;

    public CupomFiscalController(CupomFiscalService cupomFiscalService) {
        this.cupomFiscalService = cupomFiscalService;
    }

    @Operation(
            summary = "Consultar cupons fiscais (NFC-e)",
            description = "Consulta uma lista paginada de cupons fiscais com possibilidade de aplicar filtros de busca por período e dados do cliente.",
            tags = {SwaggerTags.NOTA_FISCAL},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código do cupom fiscal\n" +
                                    "- **pessoa**: Nome da pessoa associada ao cupom\n" +
                                    "- **recibo**: Número do recibo\n" +
                                    "- **datapagamento**: Data de pagamento\n" +
                                    "- **emissao**: Data de emissão\n" +
                                    "- **statusimpressao**: Status de impressão\n" +
                                    "- **valor**: Valor do cupom fiscal\n",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome da pessoa, código do cupom ou número do recibo\n" +
                                    "- **periodoPagamentoDe**: Data inicial do período de pagamento (timestamp em milissegundos)\n" +
                                    "- **periodoPagamentoAte**: Data final do período de pagamento (timestamp em milissegundos)\n" +
                                    "- **periodoEmissaoDe**: Data inicial do período de emissão (timestamp em milissegundos)\n" +
                                    "- **periodoEmissaoAte**: Data final do período de emissão (timestamp em milissegundos)",
                            example = "{\"quicksearchValue\":\"João Silva\",\"periodoPagamentoDe\":1704067200000,\"periodoPagamentoAte\":1706745599000}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListCupomFiscalPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroCupomFiscalJSON filtroCupomFiscalJSON = new FiltroCupomFiscalJSON(filtros);
            return ResponseEntityFactory.ok(cupomFiscalService.findAll(filtroCupomFiscalJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar cupom fiscal (NFC-e)",
            description = "Consulta as informações de um cupom fiscal pelo código identificador dele.",
            tags = {SwaggerTags.NOTA_FISCAL},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do cupom fiscal que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaCupomFiscal.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id){
        try {
            return ResponseEntityFactory.ok(cupomFiscalService.findById(id));
        }catch (ServiceException e){
            return  ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
