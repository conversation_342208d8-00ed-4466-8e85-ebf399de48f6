package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroQuestionarioJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.mscomunication.midiams.MediaMs;
import com.pacto.cadastrosAuxiliares.services.interfaces.QuestionarioService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.pesquisasatisfacao.ExemploRespostaPesquisaSatisfacaoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("pesquisa-satisfacao")
public class PesquisaSatisfacaoController {
    private final QuestionarioService questionarioService;

    public PesquisaSatisfacaoController(QuestionarioService questionarioService) {
        this.questionarioService = questionarioService;
    }

    @Operation(
            summary = "Consultar pesquisas de satisfação",
            description = "Consulta pesquisas de satisfação da academia com paginação e filtros de busca. Retorna questionários do tipo pesquisa de satisfação para avaliação da qualidade dos serviços oferecidos.",
            tags = {SwaggerTags.PESQUISA_SATISFACAO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código identificador do questionário\n" +
                                    "- **nomeinterno**: Nome interno do questionário\n" +
                                    "- **titulopesquisa**: Título da pesquisa de satisfação\n",
                            example = "nomeinterno,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome interno do questionário ou pelo código identificador",
                            example = "{\"quicksearchValue\":\"satisfação equipamentos\"}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaPesquisaSatisfacaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> findPesquisa(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroQuestionarioJSON filtroQuestionarioJSON = new FiltroQuestionarioJSON(filtros);
            return ResponseEntityFactory.ok(questionarioService.findPesquisa(filtroQuestionarioJSON, paginadorDTO), paginadorDTO);
        }  catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

}
