package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.TamanhoArmarioDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroTamanhoArmarioJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.TamanhoArmarioService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.respostas.tamanhoarmario.ExemploRespostaListTamanhoArmarioPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.tamanhoarmario.ExemploRespostaTamanhoArmario;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/tamanho-armario")
public class TamanhoArmarioController {
    private final TamanhoArmarioService tamanhoArmarioService;

    public TamanhoArmarioController(TamanhoArmarioService tamanhoArmarioService) {
        this.tamanhoArmarioService = tamanhoArmarioService;
    }

    @Operation(
            summary = "Incluir ou atualizar tamanho de armário",
            description = "Inclui um novo tamanho de armário ou atualiza um existente para os vestiários da academia. Se o código não for informado, será criado um novo registro. Se o código for informado, o registro existente será atualizado.",
            tags = {SwaggerTags.TAMANHO_ARMARIO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do tamanho de armário que será incluído ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TamanhoArmarioDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaTamanhoArmario.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirTamanhoArmario(@RequestBody TamanhoArmarioDTO tamanhoArmario) {
        try {
            return ResponseEntityFactory.ok(tamanhoArmarioService.saveOrUpdate(tamanhoArmario));
        } catch (ServiceException exception) {
            return ResponseEntityFactory.mensagemFront(exception.getChaveExcecao(), exception.getMessage());
        } catch (Exception exception){
            StringBuilder result = new StringBuilder(exception.toString()+"/n");
            StackTraceElement[] trace = exception.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(exception.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar tamanhos de armários",
            description = "Consulta uma lista paginada de tamanhos de armários disponíveis nos vestiários da academia com possibilidade de aplicar filtros de busca.",
            tags = {SwaggerTags.TAMANHO_ARMARIO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Ordena pelo código identificador do tamanho de armário\n" +
                                    "- **descricao**: Ordena pela descrição do tamanho de armário\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição ou pelo código do tamanho de armário",
                            example = "{\"quicksearchValue\":\"pequeno\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListTamanhoArmarioPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false)JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroTamanhoArmarioJSON filtroTamanhoArmarioJSON = new FiltroTamanhoArmarioJSON(filtros);
            return ResponseEntityFactory.ok(tamanhoArmarioService.findAll(filtroTamanhoArmarioJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString()+"\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar tamanho de armário",
            description = "Consulta as informações de um tamanho de armário específico pelo código identificador dele.",
            tags = {SwaggerTags.TAMANHO_ARMARIO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do tamanho de armário que será consultado", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaTamanhoArmario.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> tamanhoArmario(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(tamanhoArmarioService.tamanhoArmario(id));
        } catch (ServiceException exception) {
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), exception.getMessage());
        }
    }

    @Operation(
            summary = "Excluir tamanho de armário",
            description = "Exclui um tamanho de armário específico pelo código identificador dele. A exclusão só será permitida se não houver vínculos com outros registros.",
            tags = {SwaggerTags.TAMANHO_ARMARIO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do tamanho de armário que será excluído", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarPlano(@PathVariable Integer id) {
        try {
            tamanhoArmarioService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException exception) {
            return ResponseEntityFactory.mensagemFront(exception.getChaveExcecao(), exception.getMessage());
        }
    }

}
