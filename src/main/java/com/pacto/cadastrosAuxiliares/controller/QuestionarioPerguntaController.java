package com.pacto.cadastrosAuxiliares.controller;


import com.pacto.cadastrosAuxiliares.dto.basico.QuestionarioperguntaDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroQuestionarioPerguntaJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.QuestionarioPerguntaService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.questionariopergunta.ExemploRespostaListQuestionarioPergunta;
import com.pacto.cadastrosAuxiliares.swagger.respostas.questionariopergunta.ExemploRespostaQuestionarioPergunta;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/questionario-pergunta")
public class QuestionarioPerguntaController {
    private final QuestionarioPerguntaService questionarioPerguntaService;

    public QuestionarioPerguntaController(QuestionarioPerguntaService questionarioPerguntaService) {
        this.questionarioPerguntaService = questionarioPerguntaService;
    }

    @Operation(
            summary = "Consultar associações questionário-pergunta",
            description = "Consulta todas as associações entre questionários e perguntas configuradas para avaliações físicas e pesquisas de satisfação da academia.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo código da associação questionário-pergunta\n" +
                                    "- **questionario**: Filtra pelo código do questionário de avaliação física ou pesquisa de satisfação\n" +
                                    "- **pergunta**: Filtra pelo código da pergunta sobre saúde, condicionamento físico ou satisfação",
                            example = "{\"quicksearchValue\":\"8\",\"questionario\":15,\"pergunta\":1}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListQuestionarioPergunta.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroQuestionarioPerguntaJSON filtroQuestionarioPerguntaJSON = new FiltroQuestionarioPerguntaJSON(filtros);
            return ResponseEntityFactory.ok(questionarioPerguntaService.findAll(filtroQuestionarioPerguntaJSON));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar associação questionário-pergunta",
            description = "Consulta as informações de uma associação específica entre questionário e pergunta pelo código identificador.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da associação questionário-pergunta que será consultada", example = "8", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaQuestionarioPergunta.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> questionarioPergunta(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(questionarioPerguntaService.questionarioPergunta(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Criar ou atualizar associação questionário-pergunta",
            description = "Cria uma nova associação entre questionário e pergunta ou atualiza uma associação existente para configuração de avaliações físicas e pesquisas de satisfação da academia.",
            tags = {SwaggerTags.QUESTIONARIO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da associação questionário-pergunta que será criada ou atualizada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionarioperguntaDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaQuestionarioPergunta.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirQuestionarioPergunta(@RequestBody QuestionarioperguntaDTO questionarioperguntaDTO) {
        try {
            return ResponseEntityFactory.ok(questionarioPerguntaService.saveOrUpdate(questionarioperguntaDTO));
        }  catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }

    @Operation(
            summary = "Remover associação questionário-pergunta",
            description = "Remove uma associação entre questionário e pergunta pelo código identificador, desvinculando a pergunta do questionário de avaliação física ou pesquisa de satisfação.",
            tags = {SwaggerTags.QUESTIONARIO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da associação questionário-pergunta que será removida", example = "8", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarQuestionarioPergunta(@PathVariable Integer id) {
        try {
            questionarioPerguntaService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
