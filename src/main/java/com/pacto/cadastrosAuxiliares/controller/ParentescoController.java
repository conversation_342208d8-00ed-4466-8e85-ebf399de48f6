package com.pacto.cadastrosAuxiliares.controller;


import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.ParentescoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroParentescoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.ParentescoService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.parentesco.ExemploRespostaParentesco;
import com.pacto.cadastrosAuxiliares.swagger.respostas.parentesco.ExemploRespostaListParentescoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/parentesco")
public class ParentescoController {
    private final ParentescoService parentescoService;

    public ParentescoController(ParentescoService parentescoService) {
        this.parentescoService = parentescoService;
    }

    @Operation(
            summary = "Cadastrar parentesco",
            description = "Cadastra um novo parentesco ou atualiza um parentesco existente para relacionamentos familiares em academias.",
            tags = {SwaggerTags.PARENTESCO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do parentesco a ser cadastrado ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ParentescoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaParentesco.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirParentesco(@RequestBody ParentescoDTO parentesco){
        try {
            return ResponseEntityFactory.ok(parentescoService.saveOrUpdate(parentesco));
        } catch (ServiceException exception) {
            return ResponseEntityFactory.mensagemFront(exception.getChaveExcecao(), exception.getMessage());
        } catch (Exception exception){
            StringBuilder result = new StringBuilder(exception.toString()+"/n");
            StackTraceElement[] trace = exception.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(exception.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar parentescos",
            description = "Consulta uma lista paginada de parentescos com possibilidade de aplicar filtros de busca.",
            tags = {SwaggerTags.PARENTESCO},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição ou pelo código do parentesco",
                            example = "{\"quicksearchValue\":\"filho\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListParentescoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroParentescoJSON filtroParentescoJSON = new FiltroParentescoJSON(filtros);
            return ResponseEntityFactory.ok(parentescoService.findAll(filtroParentescoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString()+"\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar parentesco",
            description = "Consulta as informações de um parentesco pelo código identificador dele.",
            tags = {SwaggerTags.PARENTESCO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do parentesco que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaParentesco.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> parentesco(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(parentescoService.parentesco(id));
        } catch (ServiceException exception) {
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), exception.getMessage());
        }
    }

    @Operation(
            summary = "Deletar parentesco",
            description = "Exclui as informações de um parentesco pelo código identificador dele.",
            tags = {SwaggerTags.PARENTESCO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do parentesco que será excluído", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarPlano(@PathVariable Integer id) {
        try {
            parentescoService.delete(id);
            return  ResponseEntityFactory.ok();
        } catch (ServiceException exception) {
            return ResponseEntityFactory.mensagemFront(exception.getChaveExcecao(), exception.getMessage());
        }
    }
}
