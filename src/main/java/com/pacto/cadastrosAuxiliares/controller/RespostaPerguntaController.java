package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.RespostaPerguntaDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroRespostaPerguntaJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.RespostaPerguntaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.respostapergunta.ExemploRespostaRespostaPergunta;
import com.pacto.cadastrosAuxiliares.swagger.respostas.respostapergunta.ExemploRespostaRespostaPerguntaPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/respostaPergunta")
public class RespostaPerguntaController {
    private final RespostaPerguntaService respostaPerguntaService;

    public RespostaPerguntaController(RespostaPerguntaService respostaPerguntaService) {
        this.respostaPerguntaService = respostaPerguntaService;
    }

    @Operation(
            summary = "Consultar opções de resposta para perguntas",
            description = "Consulta as opções de resposta disponíveis para perguntas de questionários de avaliação física, saúde e satisfação da academia.",
            tags = {SwaggerTags.PERGUNTA},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código identificador da resposta\n" +
                                    "- **descricaorespota**: Descrição da opção de resposta\n" +
                                    "- **nrQuestao**: Número da questão para ordenação das respostas\n",
                            example = "nrQuestao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição da resposta ou pelo código identificador\n" +
                                    "- **pergunta**: Filtra pelas respostas de uma pergunta específica através do código identificador da pergunta",
                            example = "{\"quicksearchValue\":\"sim\",\"pergunta\":1}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaRespostaPerguntaPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroRespostaPerguntaJSON filtroRespostaPerguntaJSON = new FiltroRespostaPerguntaJSON(filtros);
            return ResponseEntityFactory.ok(respostaPerguntaService.findAll(filtroRespostaPerguntaJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar opção de resposta para pergunta",
            description = "Consulta as informações de uma opção de resposta específica para pergunta de questionário através do código identificador.",
            tags = {SwaggerTags.PERGUNTA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da opção de resposta que será consultada", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaRespostaPergunta.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}}")
    public ResponseEntity<EnvelopeRespostaDTO> respostaPergunta(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(respostaPerguntaService.respostaPergunta(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir ou alterar opção de resposta para pergunta",
            description = "Inclui uma nova opção de resposta para pergunta de questionário ou altera uma opção existente. Se o código não for informado, será criada uma nova opção de resposta.",
            tags = {SwaggerTags.PERGUNTA},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da opção de resposta que será incluída ou alterada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = RespostaPerguntaDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaRespostaPergunta.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirRespostaPergunta(@RequestBody RespostaPerguntaDTO respostaPerguntaDTO) {
        try {
            return ResponseEntityFactory.ok(respostaPerguntaService.saveOrUpdate(respostaPerguntaDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }

    @Operation(
            summary = "Excluir opção de resposta para pergunta",
            description = "Exclui uma opção de resposta para pergunta de questionário através do código identificador.",
            tags = {SwaggerTags.PERGUNTA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da opção de resposta que será excluída", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida"
                    )
            }
    )
    @DeleteMapping("/{id}")
        public ResponseEntity<EnvelopeRespostaDTO> deletarPlano(@PathVariable Integer id ) {
            try {
                respostaPerguntaService.delete(id);
                return ResponseEntityFactory.ok();
            } catch (ServiceException e) {
                return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

