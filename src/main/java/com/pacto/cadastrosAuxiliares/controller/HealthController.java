package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.health.ExemploRespostaHealth;
import com.pacto.config.security.interfaces.LeituraTokenServico;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;

@RestController
@RequestMapping("/health")
public class HealthController {

    @Operation(
            summary = "Verificar saúde do sistema",
            description = "Verifica se o sistema de gestão da academia está funcionando corretamente e disponível para uso.",
            tags = {SwaggerTags.HEALTH},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Sistema funcionando corretamente",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaHealth.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> todos() {
        return ResponseEntityFactory.ok("it's alive! v2");
    }
}
