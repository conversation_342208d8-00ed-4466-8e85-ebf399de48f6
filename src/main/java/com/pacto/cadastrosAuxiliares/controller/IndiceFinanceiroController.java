package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.IndiceFinanceiroDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroIndiceFinanceiroJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.IndiceFinanceiroService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.indicefinanceiro.ExemploRespostaIndiceFinanceiro;
import com.pacto.cadastrosAuxiliares.swagger.respostas.indicefinanceiro.ExemploRespostaListIndiceFinanceiroPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/indice-financeiro")
public class IndiceFinanceiroController {

    private final IndiceFinanceiroService service;

    public IndiceFinanceiroController(IndiceFinanceiroService service) {
        this.service = service;
    }

    @Operation(
            summary = "Consultar índices financeiros",
            description = "Consulta uma lista paginada de índices financeiros utilizados para reajuste de preços de planos de academia.",
            tags = {SwaggerTags.INDICE_FINANCEIRO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo código do índice, ano ou descrição do mês\n" +
                                    "- **tiposIndice**: Filtra pelos tipos de índice financeiro (IGPM, INPC, IPC_BR, IPCA, OUTROS)\n" +
                                    "- **tiposPlano**: Filtra pelos tipos de plano (Plano Normal, Plano Recorrência)\n" +
                                    "- **meses**: Filtra pelos meses de referência do índice",
                            example = "{\"quicksearchValue\":\"2024\",\"tiposIndice\":[4],\"tiposPlano\":[1],\"meses\":[\"01\"]}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListIndiceFinanceiroPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroIndiceFinanceiroJSON filtroIndiceFinanceiroJSON = new FiltroIndiceFinanceiroJSON(filtros);
            return ResponseEntityFactory.ok(service.findAll(filtroIndiceFinanceiroJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar índice financeiro",
            description = "Consulta as informações de um índice financeiro específico pelo código identificador.",
            tags = {SwaggerTags.INDICE_FINANCEIRO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do índice financeiro que será consultado", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaIndiceFinanceiro.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> indice(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.indice(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir ou atualizar índice financeiro",
            description = "Inclui um novo índice financeiro ou atualiza um existente para reajuste de preços de planos de academia.",
            tags = {SwaggerTags.INDICE_FINANCEIRO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do índice financeiro que será incluído ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = IndiceFinanceiroDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaIndiceFinanceiro.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluir(@RequestBody IndiceFinanceiroDTO dto) {
        try {
            return ResponseEntityFactory.ok(service.saveOrUpdate(dto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @Operation(
            summary = "Remover índice financeiro",
            description = "Remove um índice financeiro específico do sistema de reajuste de preços de planos de academia.",
            tags = {SwaggerTags.INDICE_FINANCEIRO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do índice financeiro que será removido", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id ) {
        try {
            service.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
