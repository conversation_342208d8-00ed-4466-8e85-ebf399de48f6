package com.pacto.cadastrosAuxiliares.mscomunication.midiams.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Midia", description = "Informações de mídia associada ao questionário da academia")
public class MidiaDTO {

    @Schema(description = "Chave de identificação da mídia no sistema", example = "USR123_PESQUISA_2024")
    private String chave;

    @Schema(description = "Tipo da mídia que define sua categoria de uso", example = "PESQUISA")
    private String tipo;

    @Schema(description = "Identificador único da mídia", example = "PESQUISA_IMG_1704067200000")
    private String identificador;

    @Schema(description = "Extensão do arquivo de mídia", example = "jpg")
    private String extensao;

    @Schema(description = "Dados binários do arquivo de mídia")
    private byte[] data;

    @Schema(description = "Arquivo codificado em base64 para upload", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...")
    private String arquivo;

    @Schema(description = "Nome original do arquivo de mídia", example = "background-academia.jpg")
    private String nomeArquivo;

    @Schema(description = "Indica se o identificador deve ser criptografado", example = "true")
    private Boolean encriptarIdentificador;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getExtensao() {
        return extensao;
    }

    public void setExtensao(String extensao) {
        this.extensao = extensao;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public Boolean getEncriptarIdentificador() {
        return encriptarIdentificador;
    }

    public void setEncriptarIdentificador(Boolean encriptarIdentificador) {
        this.encriptarIdentificador = encriptarIdentificador;
    }

    public String getArquivo() {
        return arquivo;
    }

    public void setArquivo(String arquivo) {
        this.arquivo = arquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }
}
