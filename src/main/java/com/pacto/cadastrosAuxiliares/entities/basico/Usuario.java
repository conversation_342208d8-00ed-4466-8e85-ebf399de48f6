package com.pacto.cadastrosAuxiliares.entities.basico;

import com.pacto.config.annotations.RelationalField;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;

@Entity
@Schema(name = "Usuário", description = "Informações do usuário responsável")
public class Usuario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código identificador único do usuário", example = "1")
    private Integer codigo;

    @Schema(description = "Nome completo do usuário", example = "João Silva")
    private String nome;

    @RelationalField
    @OneToOne
    @JoinColumn(name = "colaborador", foreignKey = @ForeignKey(name = "fk_usuario_colaborador"))
    @Schema(description = "Colaborador associado ao usuário")
    private Colaborador colaborador;

    @Schema(description = "Nome de usuário para login no sistema", example = "joao.silva")
    private String username;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Colaborador getColaborador() {
        return colaborador;
    }

    public void setColaborador(Colaborador colaborador) {
        this.colaborador = colaborador;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
