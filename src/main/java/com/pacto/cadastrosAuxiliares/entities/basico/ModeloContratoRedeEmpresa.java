package com.pacto.cadastrosAuxiliares.entities.basico;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.cadastrosAuxiliares.dto.base.RedeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Entity
@NomeEntidadeLog("Modelo Contrato Rede Empresa")
public class ModeloContratoRedeEmpresa implements Cloneable{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código identificador único do modelo de contrato rede empresa", example = "1")
    private Integer codigo;

    @Schema(description = "Código identificador do modelo de contrato de academia que será replicado", example = "15")
    private Integer modeloContrato;

    @Schema(description = "Chave identificadora da unidade da rede de academias", example = "ACADEMIA_CENTRO")
    private String chave;

    @Schema(description = "Código identificador da empresa na rede ZW", example = "123")
    private Integer empresaZw;

    @Schema(description = "Data de cadastro do modelo de contrato na rede empresa", example = "2024-01-15T10:30:00.000+00:00")
    private Date dataCadastro;

    @Schema(description = "Data da última atualização do modelo de contrato na rede empresa", example = "2024-03-20T14:45:00.000+00:00")
    private Date dataAtualizacao;

    @Schema(description = "Quantidade total de empresas na rede de academias", example = "5")
    private Integer qtdeEmpresasRede;

    @Schema(description = "Quantidade de modelos de contrato já replicados na rede", example = "3")
    private Integer qtdeModeloContratosReplicados;

    @Schema(description = "Quantidade de modelos de contrato ainda não replicados na rede", example = "2")
    private Integer qtdeModeloContratosNaoReplicados;

    @Schema(description = "Nome fantasia da unidade da academia na rede", example = "Academia Fitness Center")
    private String nomeUnidade;

    @Schema(description = "Mensagem informativa sobre a situação atual do modelo de contrato na rede", example = "REPLICADO EM 20/03/2024 14:45. O código do modelo de contrato na chave ACADEMIA_CENTRO é 25")
    private String mensagemSituacao;

    @Schema(description = "Código identificador do modelo de contrato replicado na unidade destino", example = "25")
    private Integer modeloContratoReplicado;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getModeloContrato() {
        return modeloContrato;
    }

    public void setModeloContrato(Integer modeloContrato) {
        this.modeloContrato = modeloContrato;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getEmpresaZw() {
        return empresaZw;
    }

    public void setEmpresaZw(Integer empresaZw) {
        this.empresaZw = empresaZw;
    }

    public Integer getQtdeEmpresasRede() {
        return qtdeEmpresasRede;
    }

    public void setQtdeEmpresasRede(Integer qtdeEmpresasRede) {
        this.qtdeEmpresasRede = qtdeEmpresasRede;
    }

    public Integer getQtdeModeloContratosReplicados() {
        return qtdeModeloContratosReplicados;
    }

    public void setQtdeModeloContratosReplicados(Integer qtdeModeloContratosReplicados) {
        this.qtdeModeloContratosReplicados = qtdeModeloContratosReplicados;
    }

    public Integer getQtdeModeloContratosNaoReplicados() {
        return qtdeModeloContratosNaoReplicados;
    }

    public void setQtdeModeloContratosNaoReplicados(Integer qtdeModeloContratosNaoReplicados) {
        this.qtdeModeloContratosNaoReplicados = qtdeModeloContratosNaoReplicados;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public Integer getModeloContratoReplicado() {
        return modeloContratoReplicado;
    }

    public void setModeloContratoReplicado(Integer modeloContratoReplicado) {
        this.modeloContratoReplicado = modeloContratoReplicado;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }
}
