package com.pacto.config.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Usuário Simples", description = "Informações básicas de um usuário do sistema de academias")
public class UsuarioSimplesDTO {

    @Schema(description = "Nome de usuário para login no sistema da academia", example = "carlos.instrutor")
    private String username;

    @Schema(description = "Código identificador único do usuário no sistema", example = "15")
    private Integer codZw;

    @Schema(description = "Chave de acesso do usuário para autenticação", example = "abc123def456")
    private String chave;

    @Schema(description = "Código identificador da empresa/academia associada ao usuário", example = "3")
    private Integer idEmpresa;

    @Schema(description = "Indica se o usuário possui privilégios de administrador do sistema", example = "false")
    private Boolean administrador;

    public UsuarioSimplesDTO() {
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getCodZw() {
        return codZw;
    }

    public void setCodZw(Integer codZw) {
        this.codZw = codZw;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(Integer idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public Boolean getAdministrador() {
        return administrador;
    }

    public void setAdministrador(Boolean administrador) {
        this.administrador = administrador;
    }
}
